# Component Designer

The Component Designer allows users to create, customize, and manage reusable UI components with variants, styling, and behavior configuration.

## Overview

The Component Designer provides:
- Visual component creation and editing
- Variant system for different component states
- Style customization with theme integration
- Component composition and nesting
- Real-time preview and testing
- Code generation and export

## Component Types

### 1. Primitive Components

**Basic HTML Elements**
- Text: Headings, paragraphs, spans
- Button: Various button styles and states
- Input: Text inputs, textareas, selects
- Image: Responsive images with optimization
- Container: Div, section, article wrappers
- Link: Navigation and external links

**Layout Components**
- Flex: Flexible box layouts
- Grid: CSS Grid layouts
- Stack: Vertical/horizontal stacking
- Spacer: Spacing and divider elements

### 2. Composite Components

**Form Components**
- Form: Complete form with validation
- Field Group: Label, input, and error message
- Checkbox Group: Multiple checkbox options
- Radio Group: Single selection options
- File Upload: Drag-and-drop file uploads

**Navigation Components**
- Header: Site navigation and branding
- Sidebar: Side navigation menus
- Breadcrumb: Navigation breadcrumbs
- Pagination: Page navigation controls

**Content Components**
- Card: Content containers with headers/footers
- Modal: Overlay dialogs and popups
- Tabs: Tabbed content organization
- Accordion: Collapsible content sections
- Carousel: Image and content sliders

### 3. Entity Components

Entity components provide specialized interfaces for working with data entities. For detailed information about entities and their management, see [Data Management](./05-data-management.md).

**Data Display Components**
- Entity List: Display collections of entities with filtering and pagination
- Entity Card: Individual entity preview with customizable layouts
- Entity Detail: Full entity information with relationship navigation
- Entity Table: Tabular data display with sorting and filtering

**Data Input Components**
- Entity Form: Auto-generated forms based on entity schemas
- Entity Selector: Dropdown/search interface for entity selection
- Relationship Picker: Interface for managing entity relationships
- File Manager: Upload and manage entity-attached files

*Note: Entity component behavior and data binding are detailed in the [Entity Integration](#entity-integration) section below.*

## Component Structure

### 1. Component Definition
```typescript
interface Component {
  id: string
  name: string
  type: 'primitive' | 'composite' | 'entity'
  category: string
  description: string
  props: ComponentProp[]
  variants: ComponentVariant[]
  styles: ComponentStyles
  children?: Component[]
  entityBinding?: EntityBinding
}
```

### 2. Component Props
```typescript
interface ComponentProp {
  name: string
  type: 'string' | 'number' | 'boolean' | 'color' | 'spacing' | 'entity'
  label: string
  description: string
  defaultValue: any
  required: boolean
  options?: PropOption[]
  validation?: ValidationRule[]
}
```

### 3. Component Variants
```typescript
interface ComponentVariant {
  name: string
  label: string
  description: string
  props: Record<string, any>
  styles: ComponentStyles
  conditions?: VariantCondition[]
}
```

## Variant System

### 1. Style Variants

**Button Variants**
- Primary: Main call-to-action styling
- Secondary: Supporting action styling
- Outline: Border-only styling
- Ghost: Minimal styling
- Link: Text-only styling

**Size Variants**
- xs: Extra small (24px height)
- sm: Small (32px height)
- md: Medium (40px height)
- lg: Large (48px height)
- xl: Extra large (56px height)

**State Variants**
- Default: Normal state
- Hover: Mouse hover state
- Active: Pressed/clicked state
- Disabled: Non-interactive state
- Loading: Processing state

### 2. Responsive Variants

**Breakpoint-Specific Styling**
- Mobile: < 640px
- Tablet: 640px - 1024px
- Desktop: > 1024px

**Responsive Behavior**
- Hide/show at breakpoints
- Different layouts per breakpoint
- Adaptive sizing and spacing

### 3. Conditional Variants

**Data-Driven Variants**
- Show different styles based on entity data
- Conditional rendering based on user permissions
- Dynamic styling based on application state

## Component Designer Interface

### 1. Component Canvas
- Visual component editor with drag-and-drop
- Real-time preview of changes
- Component tree structure
- Nested component editing

### 2. Properties Panel

**Basic Properties**
- Component name and description
- Category and tags
- Icon and preview image

**Style Properties**
- Layout: Position, display, dimensions
- Spacing: Margin, padding, gap
- Typography: Font, size, weight, color
- Background: Color, gradient, image
- Border: Width, style, color, radius
- Shadow: Box shadow configuration
- Effects: Opacity, transform, transition

**Behavior Properties**
- Event handlers (click, hover, focus)
- Animations and transitions
- Conditional logic
- Data binding

### 3. Variant Manager
- Create and manage component variants
- Variant preview and comparison
- Variant inheritance and overrides
- A/B testing setup

### 4. Code Preview
- Generated component code
- Props interface definition
- Usage examples
- Export options

## Advanced Features

### 1. Component Composition

**Nested Components**
- Drag components into other components
- Automatic prop passing
- Component slot system
- Layout constraints

**Component Libraries**
- Organize components into libraries
- Share components across projects
- Version control for components
- Import/export component libraries

### 2. Smart Components

**AI-Powered Generation**
- Generate components from descriptions
- Suggest component improvements
- Automatic accessibility enhancements
- Performance optimization suggestions

**Template Components**
- Pre-built component templates
- Industry-specific components
- Best practice implementations
- Customizable starting points

### 3. Testing and Validation

**Visual Testing**
- Component screenshot testing
- Cross-browser compatibility
- Responsive design validation
- Accessibility testing

**Performance Testing**
- Bundle size analysis
- Render performance metrics
- Memory usage monitoring
- Loading time optimization

## Entity Integration

Entity integration allows components to work seamlessly with data entities. For comprehensive entity management details, see [Data Management](./05-data-management.md).

### 1. Data Binding

**Entity Property Binding**
- Bind component props to entity fields with type safety
- Automatic validation based on entity field constraints
- Real-time data updates via API integration
- Relationship traversal for nested data access

**Dynamic Content Rendering**
- Display entity data with automatic formatting
- Handle different field types (text, images, dates, etc.)
- Graceful handling of missing or null data
- Support for localization and internationalization

### 2. Form Components

**Schema-Based Form Generation**
- Automatically generate forms from entity schemas (see [Entity Definition](./05-data-management.md#entity-system))
- Apply validation rules defined in entity constraints
- Support for custom field components and widgets
- Multi-step form support for complex entities

**CRUD Operations**
- Integrate with REST API endpoints (detailed in [REST API System](./05-data-management.md#rest-api-system))
- Support for create, read, update, delete operations
- Bulk operations for multiple entities
- Optimistic updates with error handling and rollback

## Styling System

### 1. Theme Integration

**Automatic Theme Application**
- Components inherit theme styles
- Theme-aware color and spacing
- Consistent typography
- Responsive breakpoints

**Custom Styling**
- Override theme styles per component
- Component-specific CSS
- Style inheritance and cascading
- CSS-in-JS support

### 2. Style Management

**Style Organization**
- Base styles for component types
- Variant-specific styles
- Responsive style overrides
- State-based styling

**Style Optimization**
- Automatic CSS optimization
- Unused style removal
- Critical CSS extraction
- Style deduplication

## Code Generation

### 1. React Components
```typescript
// Generated Button component
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  children: React.ReactNode
  onClick?: () => void
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  children,
  onClick
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md'
  const variantClasses = {
    primary: 'bg-primary-500 text-white hover:bg-primary-600',
    secondary: 'bg-secondary-500 text-white hover:bg-secondary-600',
    outline: 'border border-primary-500 text-primary-500 hover:bg-primary-50'
  }
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  }
  
  return (
    <button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        disabled && 'opacity-50 cursor-not-allowed',
        loading && 'cursor-wait'
      )}
      disabled={disabled || loading}
      onClick={onClick}
    >
      {loading && <Spinner className="mr-2" />}
      {children}
    </button>
  )
}
```

### 2. Style Exports
- CSS modules
- Styled-components
- Emotion styles
- Tailwind classes

---

*Next: [Page Designer](./04-page-designer.md)*
