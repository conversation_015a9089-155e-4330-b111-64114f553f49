# Theme Designer

The Theme Designer is a powerful visual tool that allows users to create and customize the design system for their applications. It provides real-time preview and code generation capabilities.

## Overview

The Theme Designer enables users to:
- Create comprehensive design systems
- Preview changes in real-time
- Generate consistent styling across all components
- Support light and dark mode themes
- Export theme configurations as code

## Core Features

### 1. Color System

**Primary Colors**
- Primary: Main brand color
- Secondary: Supporting brand color
- Accent: Highlight and call-to-action color

**Semantic Colors**
- Success: Green tones for positive actions
- Error: Red tones for errors and warnings
- Warning: Yellow/orange tones for cautions
- Info: Blue tones for informational content

**Neutral Colors**
- Background: Page and component backgrounds
- Surface: Card and elevated element backgrounds
- Border: Dividers and component borders
- Text: Primary, secondary, and muted text colors

**Color Configuration**:
```typescript
interface ColorSystem {
  primary: {
    50: string   // Lightest
    100: string
    200: string
    300: string
    400: string
    500: string  // Base color
    600: string
    700: string
    800: string
    900: string  // Darkest
  }
  // Similar structure for secondary, accent, success, error, warning, info
}
```

### 2. Typography System

**Font Families**
- Primary: Main text font (e.g., Inter, Roboto)
- Secondary: Heading font (e.g., Poppins, Montserrat)
- Monospace: Code and technical content (e.g., Fira Code, Monaco)

**Font Scales**
- xs: 12px
- sm: 14px
- base: 16px
- lg: 18px
- xl: 20px
- 2xl: 24px
- 3xl: 30px
- 4xl: 36px
- 5xl: 48px
- 6xl: 60px

**Typography Configuration**:
```typescript
interface TypographySystem {
  fontFamily: {
    primary: string[]
    secondary: string[]
    mono: string[]
  }
  fontSize: Record<string, string>
  fontWeight: {
    thin: 100
    light: 300
    normal: 400
    medium: 500
    semibold: 600
    bold: 700
    extrabold: 800
    black: 900
  }
  lineHeight: Record<string, string>
  letterSpacing: Record<string, string>
}
```

### 3. Spacing System

**Scale**: Based on 4px grid system
- 0: 0px
- 1: 4px
- 2: 8px
- 3: 12px
- 4: 16px
- 5: 20px
- 6: 24px
- 8: 32px
- 10: 40px
- 12: 48px
- 16: 64px
- 20: 80px
- 24: 96px
- 32: 128px

**Usage**:
- Padding and margin values
- Gap in flex and grid layouts
- Component spacing

### 4. Border System

**Border Radius**
- none: 0px
- sm: 2px
- base: 4px
- md: 6px
- lg: 8px
- xl: 12px
- 2xl: 16px
- 3xl: 24px
- full: 9999px (circular)

**Border Width**
- 0: 0px
- 1: 1px
- 2: 2px
- 4: 4px
- 8: 8px

### 5. Shadow System

**Box Shadows**
- sm: Subtle shadow for cards
- base: Standard shadow for elevated elements
- md: Medium shadow for modals
- lg: Large shadow for dropdowns
- xl: Extra large shadow for overlays
- 2xl: Maximum shadow for floating elements

**Shadow Configuration**:
```typescript
interface ShadowSystem {
  sm: string    // "0 1px 2px 0 rgb(0 0 0 / 0.05)"
  base: string  // "0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)"
  md: string    // "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)"
  lg: string    // "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)"
  xl: string    // "0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)"
  "2xl": string // "0 25px 50px -12px rgb(0 0 0 / 0.25)"
}
```

### 6. Size System

**Component Sizes**
- xs: Extra small components
- sm: Small components
- base: Default size
- lg: Large components
- xl: Extra large components

**Breakpoints**
- sm: 640px
- md: 768px
- lg: 1024px
- xl: 1280px
- 2xl: 1536px

## Theme Designer Interface

### 1. Live Preview Panel
- Real-time preview of theme changes
- Component showcase displaying all styled elements
- Toggle between light and dark modes
- Responsive preview at different breakpoints

### 2. Color Editor
- Color picker with HSL, RGB, and hex inputs
- Automatic color palette generation
- Contrast ratio checking for accessibility
- Color harmony suggestions

### 3. Typography Editor
- Font family selection with Google Fonts integration
- Font size and weight adjustments
- Line height and letter spacing controls
- Typography scale preview

### 4. Spacing Editor
- Visual spacing scale editor
- Grid system configuration
- Component spacing preview

### 5. Code Export
- Generate Tailwind CSS configuration
- Export CSS custom properties
- Generate TypeScript theme objects
- Integration with design tokens

## Advanced Features

### 1. AI-Powered Assistance

**Color Palette Generation**
- Generate harmonious color palettes from a single color
- Brand color extraction from logos
- Accessibility-compliant color suggestions

**Typography Pairing**
- AI-suggested font combinations
- Readability optimization
- Brand personality matching

### 2. Component Preview

**Live Component Showcase**
- Buttons in all variants and states
- Form elements with theme styling
- Cards and layout components
- Navigation elements
- Data display components

### 3. Accessibility Features

**Color Contrast**
- WCAG AA/AAA compliance checking
- Automatic contrast adjustment suggestions
- Color blindness simulation

**Typography**
- Readability scoring
- Font size recommendations
- Line height optimization

### 4. Theme Variants

**Light/Dark Mode**
- Automatic dark mode generation
- Manual dark mode customization
- System preference detection

**Brand Themes**
- Multiple theme variants per project
- A/B testing different themes
- Seasonal or campaign-specific themes

## Integration with Components

The theme system integrates seamlessly with all components created in the [Component Designer](./03-component-designer.md).

### 1. Automatic Styling
- All components automatically inherit theme styles
- Consistent styling across the application without manual CSS
- Theme changes propagate to all components instantly

### 2. Theme Tokens
- Design tokens provide type-safe access to theme values
- Components can reference theme tokens programmatically
- Automatic theme switching between light/dark modes

### 3. Runtime Theme Switching
- CSS custom properties enable dynamic theming
- Theme changes apply instantly without page reload
- Full browser compatibility with fallback support

*For component-specific styling and variants, see [Component Designer - Styling System](./03-component-designer.md#styling-system).*

## Export and Integration

### 1. Code Generation
```typescript
// Generated theme configuration
export const theme = {
  colors: {
    primary: {
      50: '#eff6ff',
      500: '#3b82f6',
      900: '#1e3a8a'
    }
  },
  spacing: {
    1: '0.25rem',
    2: '0.5rem',
    // ...
  }
}
```

### 2. CSS Variables
```css
:root {
  --color-primary-50: #eff6ff;
  --color-primary-500: #3b82f6;
  --color-primary-900: #1e3a8a;
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
}
```

### 3. Tailwind Integration
```javascript
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          900: '#1e3a8a'
        }
      }
    }
  }
}
```

---

*Next: [Component Designer](./03-component-designer.md)*
