# Core Concepts

This document outlines the fundamental concepts that form the foundation of our no-code platform.

## Platform Overview

Our no-code platform is built around four core pillars:

1. **Visual Design** - Theme and component systems
2. **Data Management** - Entities and database integration
3. **Business Logic** - Authentication, analytics, and workflows
4. **Deployment** - Version control and environment management

## Key Concepts

### Entities

**Definition**: Entities are the data structures that define the shape and behavior of your application's data.

**Characteristics**:
- Each entity has a defined schema with typed fields
- Fields can reference other entities (relationships)
- Support for various data types: text, numbers, dates, files, images
- Automatic API generation for CRUD operations
- Built-in validation and constraints

**Example Entity Structure**:
```typescript
interface BlogPost {
  id: string
  title: string
  content: string
  author: User // Reference to User entity
  publishedAt: Date
  tags: string[]
  featuredImage: Image
  status: 'draft' | 'published' | 'archived'
}
```

### Components

**Definition**: Reusable UI elements that can be styled, configured, and composed to build pages.

**Types**:
- **Primitive Components**: Basic HTML elements (div, button, text, image)
- **Composite Components**: Complex components built from primitives
- **Entity Components**: Components that display and interact with entity data
- **Layout Components**: Grid, flex, container components for structure

**Component Features**:
- Variants for different states/styles
- Props for configuration
- Responsive design support
- Theme integration
- Event handling

### Themes

**Definition**: A comprehensive design system that controls the visual appearance of your application.

**Theme Properties**:
- **Colors**: Primary, secondary, accent, semantic colors (success, error, warning, info)
- **Typography**: Font families, sizes, weights, line heights
- **Spacing**: Margin, padding, gap values
- **Borders**: Radius, width, colors
- **Shadows**: Box shadows and elevations
- **Sizes**: Component dimensions and breakpoints

### Pages

**Definition**: Complete views in your application that combine components, data, and business logic.

**Page Types**:
- **Static Pages**: Content-only pages (about, contact)
- **Dynamic Pages**: Data-driven pages that display entity information
- **Interactive Pages**: Pages with forms, user interactions
- **Landing Pages**: Marketing and conversion-focused pages

### Projects

**Definition**: A complete application with its own theme, components, pages, entities, and configuration.

**Project Structure**:
```
Project/
├── theme/           # Theme configuration
├── components/      # Custom components
├── pages/          # Application pages
├── entities/       # Data models
├── assets/         # Images, files, etc.
├── config/         # App configuration
└── deployment/     # Environment settings
```

## Data Flow

### 1. Design-Time Flow
```
Theme Designer → Component Designer → Page Designer → Preview
     ↓                ↓                ↓
   Styles         Components        Pages
     ↓                ↓                ↓
           Generated Application
```

### 2. Runtime Flow
```
User Request → Page Router → Component Renderer → Entity Data → Response
                ↓              ↓                    ↓
            Auth Check    Theme Application    Database Query
```

## Architecture Principles

### 1. Separation of Concerns
- **Presentation Layer**: Themes and components handle visual aspects
- **Data Layer**: Entities and database handle data management
- **Business Layer**: Authentication, authorization, and business rules
- **Infrastructure Layer**: Deployment, monitoring, and scaling

### 2. Component-Based Architecture
- Everything is a component
- Components are composable and reusable
- Clear component hierarchy and data flow
- Props-based configuration

### 3. Data-Driven Design
- Entities define application structure
- Components automatically adapt to entity schemas
- Type-safe data binding
- Automatic form generation

### 4. Theme-First Approach
- All styling goes through the theme system
- Consistent design language
- Easy theme switching (light/dark mode)
- Brand customization

## Development Workflow

### 1. Planning Phase
- Define entities and relationships
- Plan page structure and user flows
- Design component hierarchy

### 2. Design Phase
- Create or customize theme
- Build custom components
- Design page layouts

### 3. Development Phase
- Configure entity relationships
- Set up authentication and authorization
- Implement business logic

### 4. Testing Phase
- A/B test different variants
- Analytics and user behavior tracking
- Performance optimization

### 5. Deployment Phase
- Environment configuration
- Version control and rollbacks
- Monitoring and maintenance

## Integration Points

### External Services
- **Authentication**: Clerk, Auth0, custom (see [Authentication & Authorization](./06-auth.md))
- **Database**: PostgreSQL, MySQL, MongoDB (see [Data Management](./05-data-management.md))
- **Storage**: AWS S3, Cloudinary, local file storage
- **Analytics**: Segment, Google Analytics, custom (see [Business Features](./07-business-features.md))
- **Payments**: Stripe, PayPal, custom (see [Business Features](./07-business-features.md))
- **Email**: SendGrid, Mailgun, custom (see [Business Features](./07-business-features.md))

### Code Export
- Clean, readable TypeScript/JavaScript
- Modern React components
- Tailwind CSS styling
- Next.js or Vite project structure
- Full source code ownership

*For detailed code export and deployment processes, see [Development Workflow](./08-development.md).*

## Performance Considerations

### Client-Side
- Component lazy loading
- Image optimization
- Bundle splitting
- Caching strategies

### Server-Side
- Database query optimization
- API response caching
- CDN integration
- Server-side rendering

### Development
- Hot module replacement
- Fast refresh
- Incremental builds
- Real-time preview

---

*Next: [Theme Designer](./02-theme-designer.md)*
