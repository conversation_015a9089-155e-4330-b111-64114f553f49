# Data Management

The Data Management system provides comprehensive tools for defining entities, managing databases, handling migrations, providing a content management system (CMS), and generating REST APIs.

## Overview

The Data Management system includes:
- Entity definition and schema management
- Automatic database migration generation
- Built-in CMS for content management
- Relationship management between entities
- Data validation and constraints
- REST API generation for CRUD operations
- Real-time updates and webhooks

## Entity System

### 1. Entity Definition

**Core Concept**
Entities define the structure and behavior of your application's data. Each entity represents a table in the database and includes fields, relationships, and business rules.

**Entity Structure**
```typescript
interface Entity {
  id: string
  name: string
  displayName: string
  description: string
  fields: EntityField[]
  relationships: EntityRelationship[]
  constraints: EntityConstraint[]
  indexes: EntityIndex[]
  permissions: EntityPermission[]
  hooks: EntityHook[]
  metadata: EntityMetadata
}
```

### 2. Field Types

**Primitive Types**
- String: Text data with length constraints
- Number: Integer and decimal numbers
- Boolean: True/false values
- Date: Date and datetime values
- JSON: Structured data objects
- UUID: Unique identifiers

**Rich Content Types**
- RichText: HTML content with WYSIWYG editing
- Markdown: Markdown content with preview
- Code: Syntax-highlighted code blocks
- URL: Validated URL fields
- Email: Email address validation
- Phone: Phone number formatting

**Media Types**
- Image: Image uploads with optimization
- File: General file uploads
- Video: Video file handling
- Audio: Audio file management
- Gallery: Multiple image collections

**Relationship Types**
- Reference: Single entity reference
- Collection: Multiple entity references
- Embedded: Nested entity data
- Polymorphic: References to multiple entity types

### 3. Field Configuration

**Field Definition**
```typescript
interface EntityField {
  id: string
  name: string
  displayName: string
  type: FieldType
  required: boolean
  unique: boolean
  defaultValue?: any
  validation: ValidationRule[]
  metadata: FieldMetadata
  ui: FieldUIConfig
}
```

**Validation Rules**
```typescript
interface ValidationRule {
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom'
  value?: any
  message: string
  condition?: string // JavaScript expression
}
```

**UI Configuration**
```typescript
interface FieldUIConfig {
  widget: 'input' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file' | 'rich-text'
  placeholder?: string
  helpText?: string
  options?: SelectOption[]
  multiple?: boolean
  accept?: string // For file uploads
  rows?: number // For textarea
}
```

## Relationship Management

### 1. Relationship Types

**One-to-One**
- User → Profile
- Order → Invoice
- Article → SEO Settings

**One-to-Many**
- User → Posts
- Category → Products
- Author → Books

**Many-to-Many**
- Posts ↔ Tags
- Users ↔ Roles
- Products ↔ Categories

**Polymorphic**
- Comments → (Posts | Products | Users)
- Attachments → (Any Entity)
- Activities → (Multiple Entity Types)

### 2. Relationship Configuration

**Relationship Definition**
```typescript
interface EntityRelationship {
  id: string
  name: string
  type: 'one-to-one' | 'one-to-many' | 'many-to-many' | 'polymorphic'
  targetEntity: string
  foreignKey?: string
  inverseField?: string
  cascadeDelete: boolean
  required: boolean
  metadata: RelationshipMetadata
}
```

**Cascade Options**
- CASCADE: Delete related records
- SET_NULL: Set foreign key to null
- RESTRICT: Prevent deletion if related records exist
- NO_ACTION: No automatic action

### 3. Relationship Queries

**Eager Loading**
- Load related data in single query
- Prevent N+1 query problems
- Configurable depth limits
- Performance optimization

**Lazy Loading**
- Load related data on demand
- Memory efficiency
- Pagination support
- Caching strategies

## Database Management

### 1. Migration System

**Automatic Migration Generation**
- Detect entity schema changes
- Generate migration scripts
- Version control for migrations
- Rollback capabilities
- Cross-database compatibility

**Migration Types**
- Create table
- Alter table (add/remove/modify columns)
- Create/drop indexes
- Add/remove constraints
- Data transformations

**Migration Example**
```sql
-- Migration: 001_create_users_table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_users_email ON users(email);
```

### 2. Database Support

**Supported Databases**
- PostgreSQL (recommended)
- MySQL/MariaDB
- SQLite (development)
- MongoDB (document store)
- Supabase (hosted PostgreSQL)

**Connection Management**
- Connection pooling
- Read/write replicas
- Failover handling
- Performance monitoring
- Query optimization

### 3. Data Validation

**Schema Validation**
- Type checking
- Constraint validation
- Custom validation rules
- Cross-field validation
- Async validation support

**Runtime Validation**
```typescript
// Generated validation schema
const userSchema = z.object({
  email: z.string().email(),
  name: z.string().min(1).max(255),
  age: z.number().min(0).max(150).optional(),
  roles: z.array(z.string()).default([])
})
```

## Content Management System (CMS)

### 1. Entity Management Interface

**Entity List View**
- Sortable and filterable entity lists
- Bulk operations (edit, delete, export)
- Search and advanced filtering
- Pagination and infinite scroll
- Custom list layouts

**Entity Detail View**
- Form-based entity editing
- Rich text and media editing
- Relationship management
- Version history
- Audit trail

**Entity Creation**
- Step-by-step entity creation
- Template-based creation
- Bulk import from CSV/JSON
- Duplicate entity functionality
- Draft and publish workflow

### 2. Content Editing

**Rich Text Editor**
- WYSIWYG editing interface
- Markdown support
- Code syntax highlighting
- Image and media embedding
- Link management
- Table editing

**Media Management**
- Drag-and-drop file uploads
- Image editing and cropping
- File organization and tagging
- CDN integration
- Automatic optimization

**Relationship Editing**
- Entity picker interfaces
- Inline entity creation
- Relationship visualization
- Bulk relationship management
- Search and filter related entities

### 3. Workflow Management

**Draft and Publish**
- Save drafts before publishing
- Scheduled publishing
- Content approval workflows
- Version comparison
- Rollback to previous versions

**User Permissions**
- Role-based access control
- Entity-level permissions
- Field-level permissions
- Custom permission rules
- Audit logging

## REST API System

### 1. Automatic API Generation

**Entity Endpoints**
For each entity, the system automatically generates a complete set of REST endpoints:

```typescript
// Generated API endpoints for User entity
GET    /api/users           // List users with filtering and pagination
POST   /api/users           // Create new user
GET    /api/users/:id       // Get user by ID with optional relations
PUT    /api/users/:id       // Update user (full update)
PATCH  /api/users/:id       // Partial user update
DELETE /api/users/:id       // Delete user
GET    /api/users/:id/posts // Get user's related posts
```

**API Features**
- Automatic endpoint generation for all entities
- Standardized JSON response formats
- Built-in pagination and filtering
- Relationship data inclusion via query parameters
- File upload handling with multipart/form-data
- Full-text search capabilities
- Real-time updates via webhooks

### 2. API Authentication and Security

**Authentication Methods**
```http
# API Key Authentication
GET /api/users
Authorization: Bearer your-api-key

# JWT Token Authentication
GET /api/users
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Security Features**
- Rate limiting per API key/user
- Request validation and sanitization
- CORS configuration
- IP whitelisting for sensitive operations
- Audit logging for all API calls

### 3. Query Capabilities

**Filtering Examples**
```http
# Basic filtering
GET /api/posts?filter={"status":"published","authorId":"123"}

# Comparison operators
GET /api/posts?filter={"createdAt":{"gte":"2024-01-01"},"views":{"gt":100}}

# Text search
GET /api/posts?search=javascript&searchFields=title,content

# Relationship filtering
GET /api/posts?filter={"author.role":"admin"}
```

**Sorting and Pagination**
```http
# Sorting
GET /api/posts?sort=createdAt:desc,title:asc

# Pagination
GET /api/posts?limit=20&offset=40

# Include relationships
GET /api/posts?include=author,tags,comments
```

### 4. File Upload API

**File Upload Endpoint**
```http
POST /api/files
Content-Type: multipart/form-data

file: [binary data]
entityId: string (optional)
fieldName: string (optional)
```

**File Response**
```json
{
  "id": "file-123",
  "url": "https://cdn.example.com/files/file-123.jpg",
  "filename": "image.jpg",
  "size": 1024000,
  "mimeType": "image/jpeg",
  "metadata": {
    "width": 1920,
    "height": 1080
  }
}
```

### 5. Webhook System

**Webhook Configuration**
```http
POST /api/webhooks
Content-Type: application/json

{
  "url": "https://your-app.com/webhooks/platform",
  "events": ["entity.created", "entity.updated", "user.registered"],
  "secret": "your-webhook-secret",
  "active": true
}
```

**Webhook Events**
- `entity.created` - New entity created
- `entity.updated` - Entity modified
- `entity.deleted` - Entity removed
- `user.registered` - New user registration
- `file.uploaded` - File upload completed

**Event System**
```typescript
// Entity event hooks for custom business logic
interface EntityHooks {
  beforeCreate?: (data: any) => Promise<any>
  afterCreate?: (entity: any) => Promise<void>
  beforeUpdate?: (id: string, data: any) => Promise<any>
  afterUpdate?: (entity: any) => Promise<void>
  beforeDelete?: (id: string) => Promise<void>
  afterDelete?: (id: string) => Promise<void>
}
```

## Data Import/Export

### 1. Import Capabilities

**Supported Formats**
- CSV files with mapping
- JSON data import
- Excel spreadsheets
- Database dumps
- API data migration

**Import Features**
- Field mapping interface
- Data validation during import
- Error reporting and correction
- Batch processing
- Progress tracking

### 2. Export Capabilities

**Export Formats**
- CSV with custom fields
- JSON data export
- Excel reports
- PDF documents
- API data dumps

**Export Options**
- Filtered data export
- Relationship data inclusion
- Custom field selection
- Scheduled exports
- Email delivery

---

*Next: [Authentication & Authorization](./06-auth.md)*
