- welcome
- who am I?
- what is the stream about?
    - idea
    - existing products: Webflow, Framer, Builder.io, etc.
    - examples
- show prototype example

- plan for today:
    - [ ] add a copy/paste styles feature
    - [ ] font weight value isn't shown
    - [ ] the opacity for the color picker is 0 by default: make it 100%
    - [ ] add a duplicate element feature (maybe using CMD/CTRL+D)
    - [ ] drag and drop and element from one place to another on the template
    - [ ] clicking outside an text editable element should disabled text editing
    - [ ] update the selected element overlay when an element is added/deleted
    - [ ] add the Flex style
    - [ ] add "Pages and Features" similar to Odoo
    - [ ] fix color picker values being different in the original template for the "Start building" header button


- Features
    - open any React project and read the component list in that project
    - open and visualize the selected component
    - Edit HTML/JSX
    - Edit CSS style or TailwindCSS classes
    - Save edits on disk in real-time
    - CSS variables & themes
    - use the project's styling package (eg prettier) before saving
    - have prebuilt components with different styles
        - user can pick a theme from the prebuilt ones and edit it (styles, colors, spacing, borders, shadows, etc.)
    - support responsive design

- Phase 1
    - opening an HTML file
        - user clicks on the "Open project" buttons
        - picks an html file from the disk
        - render the html content on the page
    - real-time edits for HTML and CSS (inline styles for now)
    - saving the HTML back to the file
    - components

- Phase 2
    - Render React components



