<html lang="en"><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logytron - A Website Builder Made by a Developer, for Creators</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6; color: #ffffff;
            background: #0a0a0a;
            scroll-behavior: smooth; overflow-x: hidden;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 50%, rgba(99, 102, 241, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(168, 85, 247, 0.2) 0%, transparent 50%);
            z-index: -1;
            /*animation: backgroundShift 20s ease-in-out infinite;*/
        }

        /*@keyframes backgroundShift {*/
        /*    0%, 100% { transform: translateX(0) translateY(0); }*/
        /*    25% { transform: translateX(-20px) translateY(-10px); }*/
        /*    50% { transform: translateX(20px) translateY(10px); }*/
        /*    75% { transform: translateX(-10px) translateY(20px); }*/
        /*}*/

        .container { max-width: 1200px; margin: 0 auto; padding: 0 2rem; }

        /* Glass Header */
        .header {
            position: fixed; top: 1rem; left: 2rem; right: 2rem; z-index: 1000;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .nav {
            display: flex; justify-content: space-between; align-items: center;
            padding: 1rem 2rem;
        }

        .logo {
            font-size: 1.5rem; font-weight: 800;
            background: linear-gradient(135deg, hsl(248.21deg 66.06% 70.64%) 0%, hsl(0deg 0% 100%) 100%);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;
        }

        .nav-links { display: flex; gap: 2rem; list-style: none; align-items: center; }
        .nav-links a {
            text-decoration: none; color: #e2e8f0; font-weight: 500;
            transition: all 0.3s ease; padding: 0.5rem 1rem;
            border-radius: 10px;
        }
        .nav-links a:hover {
            color: #ffffff;
            background: rgba(255, 255, 255, 0.1);
        }

        .nav-cta {
            background: rgba(99, 102, 241, 0.2);
            color: #ffffff; padding: 0.75rem 1.5rem; border-radius: 12px; font-weight: 600;
            text-decoration: none; transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.3);
            backdrop-filter: blur(10px);
        }
        .nav-cta:hover {
            background: rgba(99, 102, 241, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
        }

        /* Hero Section */
        .hero {
            display: flex; align-items: center; justify-content: center;
            padding: 8rem 0 4rem;
        }

        .hero-content {
            text-align: center; max-width: 1000px; margin: 0 auto; padding: 0 2rem;
        }

        .hero h1 {
            font-size: clamp(3rem, 8vw, 5rem); font-weight: 900; margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #ffffff 0%, #6366f1 50%, #8b5cf6 100%);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;
            line-height: 1.1;
        }

        .hero p {
            font-size: 1.25rem; margin-bottom: 3rem; color: #e2e8f0;
            line-height: 1.7; max-width: 600px; margin-left: auto; margin-right: auto;
        }

        /* Glass Email Signup */
        .hero-signup {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 24px;
            padding: 2.5rem;
            margin: 3rem auto;
            max-width: 500px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
        }

        .hero-signup h3 {
            font-size: 1.5rem; font-weight: 700; margin-bottom: 1rem;
            color: #ffffff; text-align: center;
        }

        .hero-signup p {
            font-size: 1rem; margin-bottom: 2rem; color: #e2e8f0;
            text-align: center; opacity: 0.9;
        }

        .signup-form { display: flex; flex-direction: column; gap: 1rem; }

        .email-input {
            padding: 1rem 1.5rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            font-size: 1rem; color: #ffffff;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .email-input::placeholder { color: #94a3b8; }

        .email-input:focus {
            outline: none;
            border-color: rgba(99, 102, 241, 0.5);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        .hero-buttons {
            display: flex; gap: 1rem; justify-content: center;
            flex-wrap: wrap; margin-top: 2rem;
        }

        .btn {
            display: inline-flex; align-items: center; gap: 0.5rem;
            padding: 1rem 2rem; border-radius: 16px; text-decoration: none;
            font-weight: 600; font-size: 1rem; transition: all 0.3s ease;
            border: none; cursor: pointer;
        }

        .btn-primary {
            background: rgba(99, 102, 241, 0.2);
            color: #ffffff;
            border: 1px solid rgba(99, 102, 241, 0.3);
            backdrop-filter: blur(10px);
        }
        .btn-primary:hover {
            background: rgba(99, 102, 241, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(99, 102, 241, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.08);
            color: #e2e8f0;
            border: 1px solid rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
        }
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
            color: #ffffff;
        }

        /* Features Section */
        .features {
            padding: 8rem 0;
        }

        .section-title {
            text-align: center; margin-bottom: 5rem;
        }
        .section-title h2 {
            font-size: clamp(2.5rem, 6vw, 3.5rem); font-weight: 800; margin-bottom: 1.5rem;
            color: #ffffff; line-height: 1.2;
        }
        .section-title p {
            font-size: 1.2rem; color: #e2e8f0; max-width: 600px; margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2.5rem;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 1), transparent);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(99, 102, 241, 0.3);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
        }

        .feature-icon {
            width: 60px; height: 60px;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.2) 0%, rgba(139, 92, 246, 0.2) 100%);
            border: 1px solid rgba(99, 102, 241, 0.3);
            border-radius: 16px;
            display: flex; align-items: center; justify-content: center;
            margin-bottom: 1.5rem; font-size: 1.5rem;
            backdrop-filter: blur(10px);
        }

        .feature-card h3 {
            font-size: 1.5rem; font-weight: 700; margin-bottom: 1rem; color: #ffffff;
        }

        .feature-card p {
            color: #e2e8f0; line-height: 1.6; opacity: 0.9;
        }

        /* About Section */
        .about {
            padding: 8rem 0;
        }

        .about-content {
            display: grid; grid-template-columns: 1fr 1fr; gap: 4rem;
            align-items: center; max-width: 1200px; margin: 0 auto; padding: 0 2rem;
        }

        .about-text h2 {
            font-size: clamp(2.5rem, 5vw, 3rem); font-weight: 800; margin-bottom: 2rem;
            color: #ffffff; line-height: 1.2;
        }

        .about-text p {
            font-size: 1.1rem; color: #e2e8f0; margin-bottom: 1.5rem; line-height: 1.7;
        }

        .youtube-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            padding: 3rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .youtube-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #ff0000, transparent);
        }

        .youtube-icon {
            width: 80px; height: 80px;
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid rgba(255, 0, 0, 0.3);
            border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            margin: 0 auto 2rem; font-size: 2rem; color: #ff0000;
            backdrop-filter: blur(10px);
        }

        .youtube-card h3 {
            color: #ffffff; margin-bottom: 1rem; font-size: 1.5rem; font-weight: 700;
        }
        .youtube-card p {
            color: #e2e8f0; margin-bottom: 2rem; opacity: 0.9;
        }

        /* Footer */
        .footer {
            padding: 4rem 0 2rem;
            margin: 4rem 2rem 2rem;
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 20px;
        }

        .footer-content {
            text-align: center; max-width: 1200px; margin: 0 auto; padding: 0 2rem;
        }

        .footer p {
            color: #94a3b8; font-size: 0.9rem;
        }
        .footer a {
            color: #8b5cf6; text-decoration: none; font-weight: 500;
            transition: color 0.3s ease;
        }
        .footer a:hover { color: #a855f7; }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .about-content { grid-template-columns: 1fr; gap: 3rem; }
        }

        @media (max-width: 768px) {
            .header { left: 1rem; right: 1rem; }
            .nav { padding: 1rem 1.5rem; }
            .nav-links { display: none; }
            .hero { padding: 6rem 0 2rem; }
            .features-grid { grid-template-columns: 1fr; }
            .footer { margin: 2rem 1rem 1rem; }
        }
        /* Zoho Form Custom Styling */
        #sf3z8a5c4b6f1ddd5301d14170194defd7ed9b5286f030c5734b3e6aca671516cc06 {
            background: transparent !important;
        }

        #customForm {
            background: transparent !important;
        }

        .quick_form_9_css {
            background: transparent !important;
            border: none !important;
            padding: 0 !important;
            margin: 0 !important;
        }

        #SIGNUP_HEADING {
            font-size: 1.5rem !important;
            font-weight: 700 !important;
            margin-bottom: 1rem !important;
            color: #ffffff !important;
            text-align: center !important;
            background: transparent !important;
            border: none !important;
            padding: 0 !important;
        }

        .SIGNUP_FLD {
            margin-bottom: 1rem !important;
        }

        #EMBED_FORM_EMAIL_LABEL {
            width: 100% !important;
            padding: 1rem 1.5rem !important;
            background: rgba(255, 255, 255, 0.1) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            border-radius: 16px !important;
            font-size: 1rem !important;
            color: #ffffff !important;
            transition: all 0.3s ease !important;
            backdrop-filter: blur(10px) !important;
            box-sizing: border-box !important;
        }

        #EMBED_FORM_EMAIL_LABEL::placeholder {
            color: #94a3b8 !important;
        }

        #EMBED_FORM_EMAIL_LABEL:focus {
            outline: none !important;
            border-color: rgba(99, 102, 241, 0.5) !important;
            background: rgba(255, 255, 255, 0.15) !important;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2) !important;
        }

        #zcWebOptin {
            width: 100% !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            gap: 0.5rem !important;
            padding: 1rem 2rem !important;
            border-radius: 16px !important;
            text-decoration: none !important;
            font-weight: 600 !important;
            font-size: 1rem !important;
            transition: all 0.3s ease !important;
            border: 1px solid rgba(99, 102, 241, 0.3) !important;
            cursor: pointer !important;
            background: rgba(99, 102, 241, 0.2) !important;
            color: #ffffff !important;
            backdrop-filter: blur(10px) !important;
            box-sizing: border-box !important;
        }

        #zcWebOptin:hover {
            background: rgba(99, 102, 241, 0.3) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 10px 30px rgba(99, 102, 241, 0.4) !important;
        }

        #errorMsgDiv {
            display: none !important;
        }

        #Zc_SignupSuccess {
            background: rgba(34, 197, 94, 0.2) !important;
            border: 1px solid rgba(34, 197, 94, 0.3) !important;
            border-radius: 16px !important;
            backdrop-filter: blur(10px) !important;
            color: #ffffff !important;
            padding: 1rem !important;
            margin: 1rem 0 !important;
        }

        #signupSuccessMsg {
            color: #ffffff !important;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            font-size: 14px !important;
        }

        .successicon {
            filter: brightness(0) invert(1) !important;
        }
    </style>

    <!--Zoho Campaigns Web-Optin Form Scripts-->
    <script type="text/javascript" src="https://zcv3-zcmp.maillist-manage.eu/js/optin.min.js" onload="setupSF('sf3z8a5c4b6f1ddd5301d14170194defd7ed9b5286f030c5734b3e6aca671516cc06','ZCFORMVIEW',false,'light',false,'0')"></script>
    <script type="text/javascript">
        function runOnFormSubmit_sf3z8a5c4b6f1ddd5301d14170194defd7ed9b5286f030c5734b3e6aca671516cc06(th){
            /*Before submit, if you want to trigger your event, "include your code here"*/
        };
    </script>
<script src="https://maillist-manage.eu/ua/TrailEvent?callback=processData&amp;category=updImpression&amp;signupFormIx=3z8a5c4b6f1ddd5301d14170194defd7ed9b5286f030c5734b3e6aca671516cc06&amp;trackingCode=ZCFORMVIEW&amp;action=impression&amp;orgId=3zf33044cf018f3ff7e01592485b849c4d66aa676d9ae731bd87ac22b962415f35&amp;actId=3z8699855a67d6cf3fdabd51ee3224d25cf358abc53082d2d78c0689ec40239575&amp;custId=3z10fe613e137934c936304c146a5342a608293c8a3f1b9581811ecaae5df79bb8&amp;zx=14ac1d7deb&amp;visitorType=0" id="jsonp" async="false" type="application/javascript"></script><script type="application/javascript" src="https://zcv3-zcmp.maillist-manage.eu/js/dig.js"></script><style type="text/css">.ih{display: none !important;}</style></head>
<body>
    <header class="header" style="background: rgba(255, 255, 255, 0.05); border-color: rgba(255, 255, 255, 0.1);">
        <nav class="nav">
            <div class="logo">Logytron</div>
            <ul class="nav-links">
                <li><a draggable="true" href="#features">Features</a></li>
                <li><a href="#about" draggable="true">Live Streams</a></li>
                <li><a href="https://www.youtube.com/@ionelLupu_" target="_blank" class="nav-cta">Watch Live</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section class="hero">
            <div class="hero-content" draggable="true">
                <h1 draggable="true" style="margin-top: 50px; margin-bottom: 50px;">Hey! I'm Building a Website Builder for You</h1>
                <p draggable="true" style="margin-bottom: 1rem;">As a fellow developer, I know how tedious it can be to build websites from scratch every time. So I'm creating this visual editor where you can drag, drop, and see changes instantly. No more repetitive HTML/CSS!</p><div style="padding: 8px; font-size: 20px;" draggable="true">Fun fact: You're actually viewing this page inside the website builder itself right now. Feel free to play around!</div>

                <div class="hero-signup" draggable="true">
                    <p>Join me on this journey! Get updates when I add cool new features and find out when I'm coding live.</p>

                    <!--Zoho Campaigns Web-Optin Form Starts Here-->
                    <div id="sf3z8a5c4b6f1ddd5301d14170194defd7ed9b5286f030c5734b3e6aca671516cc06" data-type="signupform_0">
                        <div id="customForm">
                            <div class="quick_form_9_css" name="SIGNUP_BODY">
                                <div>
                                    <div id="SIGNUP_HEADING">Join Our Newsletter</div>
                                    <div style="position:relative;">
                                        <div id="Zc_SignupSuccess" style="display:none;position:absolute;margin-left:4%;width:90%;background-color: white; padding: 3px; border: 3px solid rgb(194, 225, 154);  margin-top: 10px;margin-bottom:10px;word-break:break-all ">
                                            <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                                <tbody>
                                                    <tr>
                                                        <td width="10%">
                                                            <img class="successicon" src="https://zcv3-zcmp.maillist-manage.eu/images/challangeiconenable.jpg" align="absmiddle">
                                                        </td>
                                                        <td>
                                                            <span id="signupSuccessMsg" style="color: rgb(73, 140, 132); font-family: sans-serif; font-size: 14px;word-break:break-word">&nbsp;&nbsp;Thank you for Signing Up</span>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <form method="POST" id="zcampaignOptinForm" action="https://zcv3-zcmp.maillist-manage.eu/weboptin.zc" target="_zcSignup">
                                        <div id="errorMsgDiv" style="opacity: 1;">Please correct the marked field(s) below.</div>
                                        <div class="SIGNUP_FLD">
                                            <input type="text" placeholder="Email" changeitem="SIGNUP_FORM_FIELD" name="CONTACT_EMAIL" id="EMBED_FORM_EMAIL_LABEL" style="border-color: rgb(242, 100, 77); border-style: solid; border-width: 1px;">
                                        </div>
                                        <div class="SIGNUP_FLD">
                                            <input type="button" name="SIGNUP_SUBMIT_BUTTON" id="zcWebOptin" value=" 🔔 Get updates" onclick="saveOptin(this,false,function runOnFormSubmit_sf3z8a5c4b6f1ddd5301d14170194defd7ed9b5286f030c5734b3e6aca671516cc06(th){
            /*Before submit, if you want to trigger your event, &quot;include your code here&quot;*/
        },'#sf3z8a5c4b6f1ddd5301d14170194defd7ed9b5286f030c5734b3e6aca671516cc06[data-type=&quot;signupform_0&quot;] ',event);">
                                        </div>
                                        <input type="hidden" id="fieldBorder" value="">
                                        <input type="hidden" id="submitType" name="submitType" value="optinCustomView">
                                        <input type="hidden" id="emailReportId" name="emailReportId" value="">
                                        <input type="hidden" id="formType" name="formType" value="QuickForm">
                                        <input type="hidden" name="zx" id="cmpZuid" value="14ac1d7deb">
                                        <input type="hidden" name="zcvers" value="2.0">
                                        <input type="hidden" name="oldListIds" id="allCheckedListIds" value="">
                                        <input type="hidden" id="mode" name="mode" value="OptinCreateView">
                                        <input type="hidden" id="zcld" name="zcld" value="15508c1756c06dd">
                                        <input type="hidden" id="zctd" name="zctd" value="">
                                        <input type="hidden" id="document_domain" value="">
                                        <input type="hidden" id="zc_Url" value="zcv3-zcmp.maillist-manage.eu">
                                        <input type="hidden" id="new_optin_response_in" value="0">
                                        <input type="hidden" id="duplicate_optin_response_in" value="0">
                                        <input type="hidden" name="zc_trackCode" id="zc_trackCode" value="ZCFORMVIEW">
                                        <input type="hidden" id="zc_formIx" name="zc_formIx" value="3z8a5c4b6f1ddd5301d14170194defd7ed9b5286f030c5734b3e6aca671516cc06">
                                        <input type="hidden" id="viewFrom" value="URL_ACTION">
                                        <span style="display: none" id="dt_CONTACT_EMAIL">1,true,6,Contact Email,2</span>
                                    <input type="text" style="display:none !important;" name="qs" class="ih"><input type="text" style="display:none !important;" name="lf" class="ih" value="1750760876833"><input type="hidden" name="di" value="114345621030767950921750760876834"></form>
                                </div>
                            </div>
                        </div>
                        <img src="https://zcv3-zcmp.maillist-manage.eu/images/spacer.gif" id="refImage" onload="referenceSetter(this)">
                    </div>
                    <!--Zoho Campaigns Web-Optin Form Ends Here-->
                </div>

                <div class="hero-buttons">
                    <a href="https://www.youtube.com/@ionelLupu_" target="_blank" class="btn btn-secondary">
                        📺 Watch Development Live
                    </a>
                </div>
            </div>
        </section>

        <section class="features" id="features">
            <div class="container">
                <div class="section-title">
                    <h2>What I'm Building for You</h2>
                    <p>These are the features I'm working on to make your life easier. Each one solves a real problem I've faced as a developer.</p>
                </div>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🎨</div>
                        <h3>Visual Drag &amp; Drop Editor</h3>
                        <p>I'm tired of writing the same HTML structure over and over. Just drag elements where you want them and see changes instantly. Way faster than coding from scratch!</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📱</div>
                        <h3>Responsive Design</h3>
                        <p>Because nobody wants to write media queries all day! Your sites will automatically look great on phones, tablets, and desktops without the headache.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <h3>Real-Time Preview</h3>
                        <p>Remember constantly refreshing your browser to see changes? Yeah, me too. This shows you exactly what you're building as you build it. No more alt-tabbing!</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎯</div>
                        <h3>Professional Templates</h3>
                        <p>I'm designing templates that don't look like every other website out there. Start with something beautiful, then make it yours with custom colors and fonts.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔧</div>
                        <h3>Advanced Styling Controls</h3>
                        <p>For when you need that perfect pixel placement! I'm building in all the CSS controls you actually use - margins, padding, flexbox, grid. No compromises.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">💾</div>
                        <h3>Export &amp; Download</h3>
                        <p>No vendor lock-in here! Download clean HTML/CSS that you actually own. Host it wherever you want or integrate it into your existing projects.</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="about" id="about">
            <div class="about-content">
                <div class="about-text">
                    <h2>Building This Live, With You</h2>
                    <p>I'm developing this entire website builder in the open through live coding sessions. You can literally watch me write every line of code, make mistakes, debug issues, and figure things out in real-time.</p>
                    <p>Come hang out while I code! Ask questions, suggest features, or just watch me struggle with CSS. It's like pair programming, but with more people and better snacks.</p>
                    <p>All streams are saved, so you can binge-watch the entire development journey from the very first line of code to where we are now.</p>
                </div>
                <div class="youtube-card">
                    <div class="youtube-icon">📺</div>
                    <h3>Live Development Streams</h3>
                    <p>Come code with me and watch this thing come to life!</p>
                    <a href="https://www.youtube.com/@ionelLupu_" target="_blank" class="btn btn-primary">
                        Subscribe to Channel
                    </a>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer" draggable="true" style="padding-top: 32px;">
        <div class="footer-content">
            <p>© 2024 Made with ❤️ by an indie developer for fellow creators and builders.
            <a href="https://www.youtube.com/@ionelLupu_" target="_blank">Follow the journey</a></p>
        </div>
    </footer>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Header scroll effect
        window.addEventListener('scroll', () => {
            const header = document.querySelector('.header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.08)';
                header.style.borderColor = 'rgba(255, 255, 255, 0.15)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.05)';
                header.style.borderColor = 'rgba(255, 255, 255, 0.1)';
            }
        });
    </script>

    <!-- Zoho Campaigns Additional Elements -->
    <input type="hidden" id="signupFormType" value="QuickForm_Horizontal">
    <div id="zcOptinOverLay" oncontextmenu="return false" style="display:none;text-align: center; background-color: rgb(0, 0, 0); opacity: 0.5; z-index: 100; position: fixed; width: 100%; top: 0px; left: 0px; height: 988px;"></div>
    <div id="zcOptinSuccessPopup" style="display:none;z-index: 9999;width: 800px; height: 40%;top: 84px;position: fixed; left: 26%;background-color: #FFFFFF;border-color: #E6E6E6; border-style: solid; border-width: 1px;  box-shadow: 0 1px 10px #424242;padding: 35px;">
        <span style="position: absolute;top: -16px;right:-14px;z-index:99999;cursor: pointer;" id="closeSuccess">
            <img src="https://zcv3-zcmp.maillist-manage.eu/images/videoclose.png">
        </span>
        <div id="zcOptinSuccessPanel"></div>
    </div>


<input type="hidden" id="zcOrgIx" value="3zf33044cf018f3ff7e01592485b849c4d66aa676d9ae731bd87ac22b962415f35"><input type="hidden" id="zcActIx" value="3z8699855a67d6cf3fdabd51ee3224d25cf358abc53082d2d78c0689ec40239575"><input type="hidden" id="zcCustIx" value="3z10fe613e137934c936304c146a5342a608293c8a3f1b9581811ecaae5df79bb8"><input type="hidden" id="zc3z8a5c4b6f1ddd5301d14170194defd7ed9b5286f030c5734b3e6aca671516cc06" value="3z38634899e1fbff66af63f7ff7ddc9abcdb0d48e66736f78b52866174ec96d34b"><input type="hidden" id="zcCntr3z8a5c4b6f1ddd5301d14170194defd7ed9b5286f030c5734b3e6aca671516cc06" value="3z8a5c4b6f1ddd5301d14170194defd7ed1eddc40e83c76bfcba4bde25354ae928"></body></html>