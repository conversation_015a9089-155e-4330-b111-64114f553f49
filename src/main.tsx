import { StrictMode, useState } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'

import * as AllIcons from 'lucide-react'
import { Input, TextField } from '@/components/ui/textfield.tsx'
import App from '@/App.tsx'

createRoot(document.getElementById('root')!).render(
    <StrictMode>
        <App />
        {/*<Icons/>*/}
    </StrictMode>,
)

function Icons() {
    const [filter, setFilter] = useState('')
    // console.log('AllIcons ->', <ArrowLeft />, <AllIcons.ArrowLeft />, Object.entries(AllIcons).map(([name, icon]) => [name, icon]))
    return <div>
        <Input value={filter} onChange={event => setFilter(event.target.value)}/>

        <div className="bg-secondary h-screen w-screen flex flex-row flex-wrap gap-2">

            {
                Object.entries(AllIcons).filter(([name, icon]) => 'render' in icon && !name.includes('Icon') && !name.includes('Lucide') &&  (filter ? name.toLowerCase().includes(filter.toLowerCase()) : true)).map(([name, icon]) => {
                    const X = icon
                    console.log('dddd ->', name, X)
                    return <div className="p-2 border border-solid border-primary/20 rounded-md  flex flex-col items-center">
                        <X key={name}/>
                        {name}
                    </div>
                })
            }</div>
    </div>
}
