import { Jolly<PERSON>extField } from '@/components/ui/textfield'
import { But<PERSON> } from '@/components/ui/button'
import { Disclosure, DisclosureHeader, DisclosurePanel } from '@/components/ui/disclosure'
import { Accessibility, Code, FormInput, Link, Tag, Type } from 'lucide-react'

interface PropertiesTabProps {
    selectedElement?: HTMLElement;
    htmlAttributes: {
        id: string;
        href: string;
    };
    handleAttributeChange: (attribute: string, value: string) => void;
    onDomChange?: () => void;
}

const PropertiesTab = ({
    selectedElement,
    htmlAttributes,
    handleAttributeChange,
    onDomChange
}: PropertiesTabProps) => {
    // Check if the selected element is a link
    const isLink = selectedElement?.tagName.toLowerCase() === 'a'

    // Function to convert element to a link
    const convertToLink = () => {
        if (!selectedElement || isLink) return

        // Create a new anchor element
        const linkElement = document.createElement('a')
        linkElement.href = '#'

        // Copy all attributes from the original element to the link
        Array.from(selectedElement.attributes).forEach(attr => {
            linkElement.setAttribute(attr.name, attr.value)
        })

        // Copy all styles from the original element to the link
        // const computedStyles = window.getComputedStyle(selectedElement)
        // Array.from(computedStyles).forEach(property => {
        //     linkElement.style.setProperty(property, computedStyles.getPropertyValue(property))
        // })

        // Move all children from the original element to the link
        while (selectedElement.firstChild) {
            linkElement.appendChild(selectedElement.firstChild)
        }

        // Replace the original element with the link
        selectedElement.parentNode?.replaceChild(linkElement, selectedElement)

        // Notify parent about DOM change
        onDomChange?.()
    }

    // Function to handle href changes
    const handleHrefChange = (value: string) => {
        if (!selectedElement || !isLink) return

        handleAttributeChange('href', value)
    }

    return (
        <div className="mt-4">
            {/* Basic Attributes */}
            <Disclosure defaultExpanded className="border-0 border-b border-b-secondary">
                <DisclosureHeader className="font-semibold flex items-center gap-2">
                    <Tag className="h-4 w-4"/>
                    Basic Attributes
                </DisclosureHeader>
                <DisclosurePanel className="pl-3">
                    <div className="flex flex-col gap-3">
                        <JollyTextField
                            label="ID"
                            aria-label="Element ID"
                            value={htmlAttributes.id}
                            onChange={(value) => handleAttributeChange('id', value)}
                            placeholder="Enter element ID"
                        />

                        <JollyTextField
                            label="CSS Classes"
                            aria-label="CSS Classes"
                            value=""
                            onChange={() => {}}
                            placeholder="Enter CSS classes"
                        />

                        <JollyTextField
                            label="Title"
                            aria-label="Title attribute"
                            value=""
                            onChange={() => {}}
                            placeholder="Tooltip text"
                        />
                    </div>
                </DisclosurePanel>
            </Disclosure>

            {/* Link Properties */}
            <Disclosure className="border-0 border-b border-b-secondary">
                <DisclosureHeader className="font-semibold flex items-center gap-2">
                    <Link className="h-4 w-4"/>
                    Link Properties
                </DisclosureHeader>
                <DisclosurePanel className="pl-3">
                    <div className="flex flex-col gap-3">
                        {isLink ? (
                            <>
                                <JollyTextField
                                    label="URL"
                                    aria-label="Link URL"
                                    value={htmlAttributes.href}
                                    onChange={handleHrefChange}
                                    placeholder="Enter URL (e.g., https://example.com)"
                                />

                                <div>
                                    <label className="text-sm font-medium">Target</label>
                                    <select className="w-full mt-1 p-2 border border-border rounded-md text-sm">
                                        <option value="">Same window</option>
                                        <option value="_blank">New window</option>
                                        <option value="_parent">Parent frame</option>
                                        <option value="_top">Top frame</option>
                                    </select>
                                </div>

                                <div>
                                    <label className="text-sm font-medium">Relationship</label>
                                    <select className="w-full mt-1 p-2 border border-border rounded-md text-sm">
                                        <option value="">None</option>
                                        <option value="nofollow">No follow</option>
                                        <option value="noopener">No opener</option>
                                        <option value="noreferrer">No referrer</option>
                                        <option value="sponsored">Sponsored</option>
                                    </select>
                                </div>
                            </>
                        ) : (
                            <Button
                                variant="outline"
                                size="sm"
                                onPress={convertToLink}
                            >
                                Convert to a Link
                            </Button>
                        )}
                    </div>
                </DisclosurePanel>
            </Disclosure>

            {/* Content Properties */}
            <Disclosure className="border-0 border-b border-b-secondary">
                <DisclosureHeader className="font-semibold flex items-center gap-2">
                    <Type className="h-4 w-4"/>
                    Content Properties
                </DisclosureHeader>
                <DisclosurePanel className="pl-3">
                    <div className="flex flex-col gap-3">
                        <JollyTextField
                            label="Text Content"
                            aria-label="Text Content"
                            value=""
                            onChange={() => {}}
                            placeholder="Element text content"
                        />

                        <JollyTextField
                            label="Alt Text"
                            aria-label="Alt Text"
                            value=""
                            onChange={() => {}}
                            placeholder="Alternative text for images"
                        />

                        <JollyTextField
                            label="Placeholder"
                            aria-label="Placeholder"
                            value=""
                            onChange={() => {}}
                            placeholder="Input placeholder text"
                        />
                    </div>
                </DisclosurePanel>
            </Disclosure>

            {/* Form Properties */}
            <Disclosure className="border-0 border-b border-b-secondary">
                <DisclosureHeader className="font-semibold flex items-center gap-2">
                    <FormInput className="h-4 w-4"/>
                    Form Properties
                </DisclosureHeader>
                <DisclosurePanel className="pl-3">
                    <div className="flex flex-col gap-3">
                        <JollyTextField
                            label="Name"
                            aria-label="Form field name"
                            value=""
                            onChange={() => {}}
                            placeholder="Form field name"
                        />

                        <JollyTextField
                            label="Value"
                            aria-label="Form field value"
                            value=""
                            onChange={() => {}}
                            placeholder="Default value"
                        />

                        <div>
                            <label className="text-sm font-medium">Input Type</label>
                            <select className="w-full mt-1 p-2 border border-border rounded-md text-sm">
                                <option value="text">Text</option>
                                <option value="email">Email</option>
                                <option value="password">Password</option>
                                <option value="number">Number</option>
                                <option value="tel">Phone</option>
                                <option value="url">URL</option>
                                <option value="date">Date</option>
                                <option value="file">File</option>
                            </select>
                        </div>

                        <div className="flex items-center gap-2">
                            <input type="checkbox" id="required"/>
                            <label htmlFor="required" className="text-sm">Required field</label>
                        </div>

                        <div className="flex items-center gap-2">
                            <input type="checkbox" id="disabled"/>
                            <label htmlFor="disabled" className="text-sm">Disabled</label>
                        </div>
                    </div>
                </DisclosurePanel>
            </Disclosure>

            {/* Accessibility */}
            <Disclosure className="border-0 border-b border-b-secondary">
                <DisclosureHeader className="font-semibold flex items-center gap-2">
                    <Accessibility className="h-4 w-4"/>
                    Accessibility
                </DisclosureHeader>
                <DisclosurePanel className="pl-3">
                    <div className="flex flex-col gap-3">
                        <JollyTextField
                            label="ARIA Label"
                            aria-label="ARIA Label"
                            value=""
                            onChange={() => {}}
                            placeholder="Accessible name for screen readers"
                        />

                        <JollyTextField
                            label="ARIA Description"
                            aria-label="ARIA Description"
                            value=""
                            onChange={() => {}}
                            placeholder="Additional description"
                        />

                        <div>
                            <label className="text-sm font-medium">ARIA Role</label>
                            <select className="w-full mt-1 p-2 border border-border rounded-md text-sm">
                                <option value="">Default</option>
                                <option value="button">Button</option>
                                <option value="link">Link</option>
                                <option value="heading">Heading</option>
                                <option value="banner">Banner</option>
                                <option value="navigation">Navigation</option>
                                <option value="main">Main</option>
                                <option value="complementary">Complementary</option>
                            </select>
                        </div>

                        <JollyTextField
                            label="Tab Index"
                            aria-label="Tab Index"
                            value=""
                            onChange={() => {}}
                            placeholder="Tab order (0, -1, or positive number)"
                        />
                    </div>
                </DisclosurePanel>
            </Disclosure>

            {/* Advanced Properties */}
            <Disclosure className="border-0">
                <DisclosureHeader className="font-semibold flex items-center gap-2">
                    <Code className="h-4 w-4"/>
                    Advanced Properties
                </DisclosureHeader>
                <DisclosurePanel className="pl-3">
                    <div className="flex flex-col gap-3">
                        <JollyTextField
                            label="Data Attributes"
                            aria-label="Data Attributes"
                            value=""
                            onChange={() => {}}
                            placeholder="data-* attributes (JSON format)"
                        />

                        <JollyTextField
                            label="Custom Attributes"
                            aria-label="Custom Attributes"
                            value=""
                            onChange={() => {}}
                            placeholder="Additional HTML attributes"
                        />

                        <div>
                            <label className="text-sm font-medium">Content Editable</label>
                            <select className="w-full mt-1 p-2 border border-border rounded-md text-sm">
                                <option value="false">False</option>
                                <option value="true">True</option>
                                <option value="plaintext-only">Plain text only</option>
                            </select>
                        </div>

                        <div className="flex items-center gap-2">
                            <input type="checkbox" id="hidden"/>
                            <label htmlFor="hidden" className="text-sm">Hidden</label>
                        </div>

                        <div className="flex items-center gap-2">
                            <input type="checkbox" id="draggable"/>
                            <label htmlFor="draggable" className="text-sm">Draggable</label>
                        </div>
                    </div>
                </DisclosurePanel>
            </Disclosure>
        </div>
    )
}

export default PropertiesTab
