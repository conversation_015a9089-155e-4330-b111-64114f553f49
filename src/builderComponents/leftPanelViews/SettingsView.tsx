import { useMemo } from 'react'
import {
    Code,
    Component,
    Globe,
    Settings
} from 'lucide-react'

export const SettingsView = () => {
    return useMemo(() => (
        <div className="w-full h-full overflow-y-auto rounded-lg shadow-sm flex flex-col">
            <h3 className="m-0 text-base font-semibold p-3 border-b border-border/50 bg-muted/30">
                ⚙️ Settings & Deploy
            </h3>
            
            <div className="flex-1 overflow-y-auto">
                {/* Project Settings */}
                <div className="p-3 border-b border-border/30">
                    <button className="w-full text-left p-3 rounded-lg border border-border hover:bg-muted/50 transition-colors">
                        <div className="flex items-center gap-3">
                            <Settings className="h-5 w-5 text-primary" />
                            <div>
                                <div className="font-medium">Project Settings</div>
                                <div className="text-xs text-muted-foreground">Configure your project</div>
                            </div>
                        </div>
                    </button>
                </div>

                {/* Deployment */}
                <div className="p-3 border-b border-border/30">
                    <button className="w-full text-left p-3 rounded-lg border border-border hover:bg-muted/50 transition-colors">
                        <div className="flex items-center gap-3">
                            <Globe className="h-5 w-5 text-primary" />
                            <div>
                                <div className="font-medium">Deploy & Publish</div>
                                <div className="text-xs text-muted-foreground">Make your site live</div>
                            </div>
                        </div>
                    </button>
                </div>

                {/* Code Export */}
                <div className="p-3 border-b border-border/30">
                    <button className="w-full text-left p-3 rounded-lg border border-border hover:bg-muted/50 transition-colors">
                        <div className="flex items-center gap-3">
                            <Code className="h-5 w-5 text-primary" />
                            <div>
                                <div className="font-medium">Export Code</div>
                                <div className="text-xs text-muted-foreground">Download your project files</div>
                            </div>
                        </div>
                    </button>
                </div>

                {/* Integrations */}
                <div className="p-3">
                    <button className="w-full text-left p-3 rounded-lg border border-border hover:bg-muted/50 transition-colors">
                        <div className="flex items-center gap-3">
                            <Component className="h-5 w-5 text-primary" />
                            <div>
                                <div className="font-medium">Integrations</div>
                                <div className="text-xs text-muted-foreground">Connect external services</div>
                            </div>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    ), [])
}
