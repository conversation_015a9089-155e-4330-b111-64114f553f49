import { useMemo } from 'react'
import {
    BarChart3,
    Mail,
    ShoppingCart,
    User
} from 'lucide-react'

export const BusinessView = () => {
    return useMemo(() => (
        <div className="w-full h-full overflow-y-auto rounded-lg shadow-sm flex flex-col">
            <h3 className="m-0 text-base font-semibold p-3 border-b border-border/50 bg-muted/30">
                📊 Business & Marketing
            </h3>
            
            <div className="flex-1 overflow-y-auto">
                {/* Analytics & Testing */}
                <div className="p-3 border-b border-border/30">
                    <button className="w-full text-left p-3 rounded-lg border border-border hover:bg-muted/50 transition-colors">
                        <div className="flex items-center gap-3">
                            <BarChart3 className="h-5 w-5 text-primary" />
                            <div>
                                <div className="font-medium">Analytics & A/B Testing</div>
                                <div className="text-xs text-muted-foreground">Track performance and optimize</div>
                            </div>
                        </div>
                    </button>
                </div>

                {/* E-commerce */}
                <div className="p-3 border-b border-border/30">
                    <button className="w-full text-left p-3 rounded-lg border border-border hover:bg-muted/50 transition-colors">
                        <div className="flex items-center gap-3">
                            <ShoppingCart className="h-5 w-5 text-primary" />
                            <div>
                                <div className="font-medium">E-commerce & Payments</div>
                                <div className="text-xs text-muted-foreground">Sell products and accept payments</div>
                            </div>
                        </div>
                    </button>
                </div>

                {/* Email Marketing */}
                <div className="p-3 border-b border-border/30">
                    <button className="w-full text-left p-3 rounded-lg border border-border hover:bg-muted/50 transition-colors">
                        <div className="flex items-center gap-3">
                            <Mail className="h-5 w-5 text-primary" />
                            <div>
                                <div className="font-medium">Email Marketing</div>
                                <div className="text-xs text-muted-foreground">Build and send email campaigns</div>
                            </div>
                        </div>
                    </button>
                </div>

                {/* User Management */}
                <div className="p-3">
                    <button className="w-full text-left p-3 rounded-lg border border-border hover:bg-muted/50 transition-colors">
                        <div className="flex items-center gap-3">
                            <User className="h-5 w-5 text-primary" />
                            <div>
                                <div className="font-medium">Users & Authentication</div>
                                <div className="text-xs text-muted-foreground">Manage users and permissions</div>
                            </div>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    ), [])
}
