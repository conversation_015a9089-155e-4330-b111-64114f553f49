import { useMemo } from 'react'
import {
    AppWindow as Tabs,
    AppWindowMac as Modal,
    BarChart3,
    Calendar,
    Component,
    MessageCircle,
    Palette,
    Search,
    Table
} from 'lucide-react'

// Helper component for draggable component items
const ComponentItem = ({ icon: Icon, label, component }: { icon: any, label: string, component: string }) => (
    <div
        className="flex flex-col items-center justify-center p-2 border border-dashed border-border/50 rounded-lg hover:bg-muted/30 cursor-move text-center"
        draggable={true}
        onDragStart={(e) => {
            e.dataTransfer.setData('component', component)
        }}
    >
        <Icon className="h-5 w-5 text-muted-foreground mb-1"/>
        <span className="text-xs">{label}</span>
    </div>
)

export const DesignView = () => {
    return useMemo(() => (
        <div className="w-full h-full overflow-y-auto rounded-lg shadow-sm flex flex-col">
            <h3 className="m-0 text-base font-semibold p-3 border-b border-border/50 bg-muted/30">
                🎨 Design & Style
            </h3>
            
            <div className="flex-1 overflow-y-auto">
                {/* Theme Designer */}
                <div className="p-3 border-b border-border/30">
                    <button className="w-full text-left p-3 rounded-lg border border-border hover:bg-muted/50 transition-colors">
                        <div className="flex items-center gap-3">
                            <Palette className="h-5 w-5 text-primary" />
                            <div>
                                <div className="font-medium">Theme Designer</div>
                                <div className="text-xs text-muted-foreground">Colors, fonts, and spacing</div>
                            </div>
                        </div>
                    </button>
                </div>

                {/* Quick Style Options */}
                <div className="p-3 border-b border-border/30">
                    <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
                        <Palette className="h-4 w-4" />
                        Quick Styles
                    </h4>
                    
                    <div className="space-y-3">
                        <div>
                            <div className="text-xs font-medium mb-2">Color Themes</div>
                            <div className="grid grid-cols-4 gap-2">
                                <button className="w-8 h-8 rounded-md bg-blue-500 border border-border" title="Blue Theme"></button>
                                <button className="w-8 h-8 rounded-md bg-green-500 border border-border" title="Green Theme"></button>
                                <button className="w-8 h-8 rounded-md bg-purple-500 border border-border" title="Purple Theme"></button>
                                <button className="w-8 h-8 rounded-md bg-orange-500 border border-border" title="Orange Theme"></button>
                            </div>
                        </div>
                        
                        <div>
                            <div className="text-xs font-medium mb-2">Font Styles</div>
                            <div className="space-y-1">
                                <button className="w-full text-left p-2 text-xs rounded border border-border hover:bg-muted/50" style={{fontFamily: 'Inter'}}>
                                    Modern (Inter)
                                </button>
                                <button className="w-full text-left p-2 text-xs rounded border border-border hover:bg-muted/50" style={{fontFamily: 'serif'}}>
                                    Classic (Serif)
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Advanced Components */}
                <div className="p-3">
                    <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
                        <Component className="h-4 w-4" />
                        Advanced Elements
                    </h4>
                    <div className="text-xs text-muted-foreground mb-3">Interactive and data elements</div>
                    
                    <div className="space-y-3">
                        <div>
                            <h5 className="text-xs font-medium mb-2 text-muted-foreground uppercase tracking-wide">Interactive</h5>
                            <div className="grid grid-cols-2 gap-2">
                                <ComponentItem icon={Tabs} label="Tabs" component="tabs" />
                                <ComponentItem icon={Modal} label="Popup" component="modal" />
                                <ComponentItem icon={MessageCircle} label="Tooltip" component="tooltip" />
                            </div>
                        </div>
                        
                        <div>
                            <h5 className="text-xs font-medium mb-2 text-muted-foreground uppercase tracking-wide">Data Display</h5>
                            <div className="grid grid-cols-2 gap-2">
                                <ComponentItem icon={Table} label="Table" component="table" />
                                <ComponentItem icon={BarChart3} label="Chart" component="chart" />
                                <ComponentItem icon={Calendar} label="Calendar" component="calendar" />
                                <ComponentItem icon={Search} label="Search" component="search" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    ), [])
}
