import { useMemo } from 'react'
import { Tree } from '@/components/ui/tree'
import { HtmlNode } from '../../lib/types'
import {
    Box,
    Camera,
    Component,
    FormInput,
    Grid3X3,
    Image,
    Layers,
    List,
    Navigation,
    Play,
    Square,
    SquareAsterisk as Card,
    Type,
    Upload,
    Video,
    Volume2
} from 'lucide-react'

// Helper component for draggable component items
const ComponentItem = ({ icon: Icon, label, component }: { icon: any, label: string, component: string }) => (
    <div
        className="flex flex-col items-center justify-center p-2 border border-dashed border-border/50 rounded-lg hover:bg-muted/30 cursor-move text-center"
        draggable={true}
        onDragStart={(e) => {
            e.dataTransfer.setData('component', component)
        }}
    >
        <Icon className="h-5 w-5 text-muted-foreground mb-1"/>
        <span className="text-xs">{label}</span>
    </div>
)

interface BuildViewProps {
    htmlStructure?: HtmlNode
    renderTreeItems: (node: HtmlNode) => React.ReactNode
}

export const BuildView = ({ htmlStructure, renderTreeItems }: BuildViewProps) => {
    return useMemo(() => (
        <div className="w-full h-full overflow-y-auto rounded-lg shadow-sm flex flex-col">
            <h3 className="m-0 text-base font-semibold p-3 border-b border-border/50 bg-muted/30">
                🏗️ Build Your Page
            </h3>
            
            <div className="flex-1 overflow-y-auto">
                {/* Page Structure */}
                <div className="p-3 border-b border-border/30">
                    <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                        <Layers className="h-4 w-4" />
                        Page Structure
                    </h4>
                    <div className="text-xs text-muted-foreground mb-2">See how your page is organized</div>
                    {htmlStructure ? (
                        <Tree defaultExpandedKeys={[htmlStructure.id, ...htmlStructure.children.flatMap(child => [child.id, ...child.children.flatMap(child => child.id)])]}>
                            {renderTreeItems(htmlStructure)}
                        </Tree>
                    ) : (
                        <div className="p-3 text-muted-foreground text-center text-sm bg-muted/30 rounded-md">
                            Start building your page
                        </div>
                    )}
                </div>

                {/* Add Elements */}
                <div className="p-3">
                    <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                        <Component className="h-4 w-4" />
                        Add Elements
                    </h4>
                    <div className="text-xs text-muted-foreground mb-3">Drag these onto your page</div>
                    
                    {/* Essential Elements */}
                    <div className="mb-4">
                        <h5 className="text-xs font-medium mb-2 text-muted-foreground uppercase tracking-wide">Essential</h5>
                        <div className="grid grid-cols-2 gap-2">
                            <ComponentItem icon={Type} label="Text" component="text" />
                            <ComponentItem icon={Square} label="Button" component="button" />
                            <ComponentItem icon={Image} label="Image" component="image" />
                            <ComponentItem icon={Box} label="Container" component="box" />
                        </div>
                    </div>

                    {/* Layout Elements */}
                    <div className="mb-4">
                        <h5 className="text-xs font-medium mb-2 text-muted-foreground uppercase tracking-wide">Layout</h5>
                        <div className="grid grid-cols-2 gap-2">
                            <ComponentItem icon={Grid3X3} label="Grid" component="grid" />
                            <ComponentItem icon={Layers} label="Flex Box" component="flex" />
                            <ComponentItem icon={Navigation} label="Header" component="header" />
                            <ComponentItem icon={Card} label="Card" component="card" />
                        </div>
                    </div>

                    {/* Form Elements */}
                    <div className="mb-4">
                        <h5 className="text-xs font-medium mb-2 text-muted-foreground uppercase tracking-wide">Forms</h5>
                        <div className="grid grid-cols-2 gap-2">
                            <ComponentItem icon={FormInput} label="Text Input" component="input" />
                            <ComponentItem icon={FormInput} label="Text Area" component="textarea" />
                            <ComponentItem icon={List} label="Dropdown" component="select" />
                            <ComponentItem icon={Upload} label="File Upload" component="file-upload" />
                        </div>
                    </div>

                    {/* Media Elements */}
                    <div>
                        <h5 className="text-xs font-medium mb-2 text-muted-foreground uppercase tracking-wide">Media</h5>
                        <div className="grid grid-cols-2 gap-2">
                            <ComponentItem icon={Video} label="Video" component="video" />
                            <ComponentItem icon={Camera} label="Gallery" component="gallery" />
                            <ComponentItem icon={Volume2} label="Audio" component="audio" />
                            <ComponentItem icon={Play} label="Media Player" component="media-player" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    ), [htmlStructure, renderTreeItems])
}
