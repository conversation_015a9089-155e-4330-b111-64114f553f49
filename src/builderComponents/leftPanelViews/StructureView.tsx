import { useMemo } from 'react'
import { Tree } from '@/components/ui/tree'
import { HtmlNode } from '../../lib/types'
import { Layers } from 'lucide-react'

interface StructureViewProps {
    htmlStructure?: HtmlNode
    renderTreeItems: (node: HtmlNode) => React.ReactNode
}

export const StructureView = ({ htmlStructure, renderTreeItems }: StructureViewProps) => {
    return useMemo(() => (
        <div className="w-full h-full overflow-y-auto rounded-lg shadow-sm flex flex-col">
            <h3 className="m-0 text-base font-semibold p-3 border-b border-border/50 bg-muted/30">
                🏗️ Page Structure
            </h3>
            
            <div className="flex-1 overflow-y-auto">
                {/* Page Structure */}
                <div className="p-3">
                    <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                        <Layers className="h-4 w-4" />
                        Element Hierarchy
                    </h4>
                    <div className="text-xs text-muted-foreground mb-2">See how your page is organized</div>
                    {htmlStructure ? (
                        <Tree defaultExpandedKeys={[htmlStructure.id, ...htmlStructure.children.flatMap(child => [child.id, ...child.children.flatMap(child => child.id)])]}>
                            {renderTreeItems(htmlStructure)}
                        </Tree>
                    ) : (
                        <div className="p-3 text-muted-foreground text-center text-sm bg-muted/30 rounded-md">
                            Start building your page
                        </div>
                    )}
                </div>
            </div>
        </div>
    ), [htmlStructure, renderTreeItems])
}
