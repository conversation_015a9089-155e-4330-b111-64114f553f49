import { useMemo } from 'react'
import {
    Database,
    FileText,
    Image,
    Layout,
    Upload,
    User,
    Video,
    Volume2
} from 'lucide-react'

export const ContentView = () => {
    return useMemo(() => (
        <div className="w-full h-full overflow-y-auto rounded-lg shadow-sm flex flex-col">
            <h3 className="m-0 text-base font-semibold p-3 border-b border-border/50 bg-muted/30">
                📄 Content & Data
            </h3>
            
            <div className="flex-1 overflow-y-auto">
                {/* Pages */}
                <div className="p-3 border-b border-border/30">
                    <button className="w-full text-left p-3 rounded-lg border border-border hover:bg-muted/50 transition-colors">
                        <div className="flex items-center gap-3">
                            <Layout className="h-5 w-5 text-primary" />
                            <div>
                                <div className="font-medium">Pages</div>
                                <div className="text-xs text-muted-foreground">Manage your website pages</div>
                            </div>
                        </div>
                    </button>
                    
                    <div className="mt-3 space-y-2">
                        <div className="p-2 border border-dashed border-border/50 rounded-lg">
                            <div className="flex items-center gap-2">
                                <Layout className="h-4 w-4 text-muted-foreground" />
                                <span className="text-sm font-medium">Home</span>
                                <span className="text-xs text-green-600 ml-auto">Published</span>
                            </div>
                        </div>
                        <div className="p-2 border border-dashed border-border/50 rounded-lg">
                            <div className="flex items-center gap-2">
                                <FileText className="h-4 w-4 text-muted-foreground" />
                                <span className="text-sm font-medium">About</span>
                                <span className="text-xs text-yellow-600 ml-auto">Draft</span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Data & Database */}
                <div className="p-3 border-b border-border/30">
                    <button className="w-full text-left p-3 rounded-lg border border-border hover:bg-muted/50 transition-colors">
                        <div className="flex items-center gap-3">
                            <Database className="h-5 w-5 text-primary" />
                            <div>
                                <div className="font-medium">Data & Database</div>
                                <div className="text-xs text-muted-foreground">Store and manage your content</div>
                            </div>
                        </div>
                    </button>
                    
                    <div className="mt-3 space-y-2">
                        <div className="p-2 border border-dashed border-border/50 rounded-lg">
                            <div className="flex items-center gap-2">
                                <User className="h-4 w-4 text-muted-foreground" />
                                <span className="text-sm font-medium">Users</span>
                                <span className="text-xs text-muted-foreground ml-auto">5 fields</span>
                            </div>
                        </div>
                        <div className="p-2 border border-dashed border-border/50 rounded-lg">
                            <div className="flex items-center gap-2">
                                <FileText className="h-4 w-4 text-muted-foreground" />
                                <span className="text-sm font-medium">Blog Posts</span>
                                <span className="text-xs text-muted-foreground ml-auto">8 fields</span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Media & Assets */}
                <div className="p-3">
                    <button className="w-full text-left p-3 rounded-lg border border-border hover:bg-muted/50 transition-colors">
                        <div className="flex items-center gap-3">
                            <Upload className="h-5 w-5 text-primary" />
                            <div>
                                <div className="font-medium">Media & Assets</div>
                                <div className="text-xs text-muted-foreground">Images, videos, and files</div>
                            </div>
                        </div>
                    </button>
                    
                    <div className="mt-3 grid grid-cols-2 gap-2">
                        <div className="aspect-square border border-dashed border-border/50 rounded-lg flex items-center justify-center">
                            <Image className="h-6 w-6 text-muted-foreground" />
                        </div>
                        <div className="aspect-square border border-dashed border-border/50 rounded-lg flex items-center justify-center">
                            <Video className="h-6 w-6 text-muted-foreground" />
                        </div>
                        <div className="aspect-square border border-dashed border-border/50 rounded-lg flex items-center justify-center">
                            <FileText className="h-6 w-6 text-muted-foreground" />
                        </div>
                        <div className="aspect-square border border-dashed border-border/50 rounded-lg flex items-center justify-center">
                            <Volume2 className="h-6 w-6 text-muted-foreground" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    ), [])
}
