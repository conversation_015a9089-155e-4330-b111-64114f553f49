import { useMemo } from 'react'
import { Tooltip, TooltipTrigger } from '@/components/ui/tooltip'
import {
    Box,
    Camera,
    Component,
    FormInput,
    Grid3X3,
    Image,
    Layers,
    List,
    Navigation,
    Play,
    Square,
    SquareAsterisk as Card,
    Type,
    Upload,
    Video,
    Volume2
} from 'lucide-react'

// Helper component for draggable component items
const ComponentItem = ({ icon: Icon, label, component, tooltip }: { icon: any, label: string, component: string, tooltip?: string }) => (
    <TooltipTrigger>
        <div
            className="flex flex-col items-center justify-center p-2 border border-dashed border-border/50 rounded-lg hover:bg-muted/30 cursor-move text-center"
            draggable={true}
            onDragStart={(e) => {
                e.dataTransfer.setData('component', component)
            }}
        >
            <Icon className="h-5 w-5 text-muted-foreground mb-1"/>
            <span className="text-xs">{label}</span>
        </div>
        {tooltip && <Tooltip>{tooltip}</Tooltip>}
    </TooltipTrigger>
)

export const ComponentsView = () => {
    return useMemo(() => (
        <div className="w-full h-full overflow-y-auto rounded-lg shadow-sm flex flex-col">
            <h3 className="m-0 text-base font-semibold p-3 border-b border-border/50 bg-muted/30">
                🧩 Add Components
            </h3>
            
            <div className="flex-1 overflow-y-auto">
                {/* Add Elements */}
                <div className="p-3">
                    <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                        <Component className="h-4 w-4" />
                        Drag & Drop Elements
                    </h4>
                    <div className="text-xs text-muted-foreground mb-3">Drag these onto your page</div>
                    
                    {/* Essential Elements */}
                    <div className="mb-4">
                        <h5 className="text-xs font-medium mb-2 text-muted-foreground uppercase tracking-wide">Essential</h5>
                        <div className="grid grid-cols-2 gap-2">
                            <ComponentItem icon={Type} label="Text" component="text" tooltip="Add text content like headings, paragraphs, and labels" />
                            <ComponentItem icon={Square} label="Button" component="button" tooltip="Interactive button for user actions and navigation" />
                            <ComponentItem icon={Image} label="Image" component="image" tooltip="Display images, photos, and graphics" />
                            <ComponentItem icon={Box} label="Container" component="box" tooltip="Generic container for grouping other elements" />
                        </div>
                    </div>

                    {/* Layout Elements */}
                    <div className="mb-4">
                        <h5 className="text-xs font-medium mb-2 text-muted-foreground uppercase tracking-wide">Layout</h5>
                        <div className="grid grid-cols-2 gap-2">
                            <ComponentItem icon={Grid3X3} label="Grid" component="grid" tooltip="CSS Grid layout for complex 2D layouts" />
                            <ComponentItem icon={Layers} label="Flex Box" component="flex" tooltip="Flexible box layout for responsive designs" />
                            <ComponentItem icon={Navigation} label="Header" component="header" tooltip="Page header with navigation and branding" />
                            <ComponentItem icon={Card} label="Card" component="card" tooltip="Content card with border and shadow" />
                        </div>
                    </div>

                    {/* Form Elements */}
                    <div className="mb-4">
                        <h5 className="text-xs font-medium mb-2 text-muted-foreground uppercase tracking-wide">Forms</h5>
                        <div className="grid grid-cols-2 gap-2">
                            <ComponentItem icon={FormInput} label="Text Input" component="input" tooltip="Single-line text input field" />
                            <ComponentItem icon={FormInput} label="Text Area" component="textarea" tooltip="Multi-line text input for longer content" />
                            <ComponentItem icon={List} label="Dropdown" component="select" tooltip="Dropdown selection menu with options" />
                            <ComponentItem icon={Upload} label="File Upload" component="file-upload" tooltip="File upload input for documents and media" />
                        </div>
                    </div>

                    {/* Media Elements */}
                    <div>
                        <h5 className="text-xs font-medium mb-2 text-muted-foreground uppercase tracking-wide">Media</h5>
                        <div className="grid grid-cols-2 gap-2">
                            <ComponentItem icon={Video} label="Video" component="video" tooltip="Embed video content from files or URLs" />
                            <ComponentItem icon={Camera} label="Gallery" component="gallery" tooltip="Image gallery with thumbnails and lightbox" />
                            <ComponentItem icon={Volume2} label="Audio" component="audio" tooltip="Audio player for music and sound files" />
                            <ComponentItem icon={Play} label="Media Player" component="media-player" tooltip="Advanced media player with controls" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    ), [])
}
