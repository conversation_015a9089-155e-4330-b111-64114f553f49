import { HtmlNode } from '../lib/types'
import { TreeItem, TreeItemContent, TreeItemExpandButton } from '@/components/ui/tree'
import { Toggle, ToggleButtonGroup } from '@/components/ui/toggle'
import { Tooltip, TooltipTrigger } from '@/components/ui/tooltip'
import { useMemo, useState } from 'react'
import { StructureView, ComponentsView, BusinessView, ContentView, DesignView, SettingsView } from './leftPanelViews'
import { BarChart3, Component, Database, Layers, Palette, Settings } from 'lucide-react'
import { cn } from '@/lib/utils.ts'

interface StructurePanelProps {
    htmlStructure?: HtmlNode;
    selectedElement?: HTMLElement;
    selectedElementPath?: number[];
    onElementSelect?: (nodeId: string) => void;
}

export default function LeftPanel({
    htmlStructure,
    selectedElementPath,
    onElementSelect
}: StructurePanelProps) {
    const [activeView, setActiveView] = useState<'structure' | 'components' | 'design' | 'content' | 'business' | 'settings'>('structure')

    // Handle click on a tree item
    const handleTreeItemClick = (node: HtmlNode) => {
        onElementSelect?.(node.id)
    }

    // Convert HtmlNode to a format compatible with the Tree component
    const renderTreeItems = (node: HtmlNode, nodePath: number[] = []) => {
        return (
            <TreeItem
                key={node.id}
                id={node.id}
                textValue={node.id}
                className={cn('hover:bg-secondary', {
                    'bg-primary/10 text-primary': JSON.stringify(selectedElementPath) === JSON.stringify(nodePath)
                })}
            >
                <TreeItemContent>
                    {node.children.length > 0 && <TreeItemExpandButton/>}
                    <button
                        onClick={() => handleTreeItemClick(node)}
                        className="flex-1 text-left outline-none bg-transparent border-none cursor-pointer"
                    >
                        <span className="font-mono text-sm font-medium capitalize">{tagNameToReadableTagName(node.tagName)}</span>
                        {/*{node.attributes.id && <span className="ml-1.5 text-purple-600 font-mono text-xs">#{node.attributes.id}</span>}*/}
                        {/*{node.attributes.class && <span className="ml-1.5 text-emerald-600 font-mono text-xs">.{node.attributes.class}</span>}*/}
                    </button>
                </TreeItemContent>

                {node.children.length > 0 && (
                    node.children.map((child, index) => renderTreeItems(child, [...nodePath, index]))
                )}
            </TreeItem>
        )
    }

    const renderStructureView = useMemo(() => (
        <StructureView htmlStructure={htmlStructure} renderTreeItems={renderTreeItems}/>
    ), [htmlStructure, renderTreeItems])

    const renderComponentsView = useMemo(() => (
        <ComponentsView/>
    ), [])

    const renderDesignView = useMemo(() => (
        <DesignView/>
    ), [])

    const renderContentView = useMemo(() => (
        <ContentView/>
    ), [])

    const renderBusinessView = useMemo(() => (
        <BusinessView/>
    ), [])

    const renderSettingsView = useMemo(() => (
        <SettingsView/>
    ), [])

    const getCurrentView = () => {
        switch (activeView) {
            case 'structure':
                return renderStructureView
            case 'components':
                return renderComponentsView
            case 'design':
                return renderDesignView
            case 'content':
                return renderContentView
            case 'business':
                return renderBusinessView
            case 'settings':
                return renderSettingsView
            default:
                return renderStructureView
        }
    }

    return (
        <div className="flex h-full">
            <div className="flex-shrink-0 border-r border-border/50 p-2">
                <ToggleButtonGroup
                    orientation="vertical"
                    selectionMode="single"
                    defaultSelectedKeys={['structure']}
                    className="flex flex-col gap-2"
                >
                    <TooltipTrigger>
                        <Toggle
                            isSelected={activeView === 'structure'}
                            onChange={() => setActiveView('structure')}
                            aria-label="Structure View"
                        >
                            <Layers className="h-4 w-4"/>
                        </Toggle>
                        <Tooltip placement="right">Page Structure - View and organize your page elements</Tooltip>
                    </TooltipTrigger>

                    <TooltipTrigger>
                        <Toggle
                            isSelected={activeView === 'components'}
                            onChange={() => setActiveView('components')}
                            aria-label="Components View"
                        >
                            <Component className="h-4 w-4"/>
                        </Toggle>
                        <Tooltip placement="right">Add Components - Drag and drop elements onto your page</Tooltip>
                    </TooltipTrigger>

                    <TooltipTrigger>
                        <Toggle
                            isSelected={activeView === 'design'}
                            onChange={() => setActiveView('design')}
                            aria-label="Design View"
                        >
                            <Palette className="h-4 w-4"/>
                        </Toggle>
                        <Tooltip placement="right">Design System - Manage colors, fonts, and themes</Tooltip>
                    </TooltipTrigger>

                    <TooltipTrigger>
                        <Toggle
                            isSelected={activeView === 'content'}
                            onChange={() => setActiveView('content')}
                            aria-label="Content View"
                        >
                            <Database className="h-4 w-4"/>
                        </Toggle>
                        <Tooltip placement="right">Content Management - Manage your site content and data</Tooltip>
                    </TooltipTrigger>

                    <TooltipTrigger>
                        <Toggle
                            isSelected={activeView === 'business'}
                            onChange={() => setActiveView('business')}
                            aria-label="Business View"
                        >
                            <BarChart3 className="h-4 w-4"/>
                        </Toggle>
                        <Tooltip placement="right">Analytics & Business - Track performance and insights</Tooltip>
                    </TooltipTrigger>

                    <TooltipTrigger>
                        <Toggle
                            isSelected={activeView === 'settings'}
                            onChange={() => setActiveView('settings')}
                            aria-label="Settings View"
                        >
                            <Settings className="h-4 w-4"/>
                        </Toggle>
                        <Tooltip placement="right">Project Settings - Configure your project and deployment</Tooltip>
                    </TooltipTrigger>
                </ToggleButtonGroup>
            </div>
            <div className="flex-grow">
                {getCurrentView()}
            </div>
        </div>
    )
};

const tagNamesToFullNameMapping: Record<string, string> = {
    nav: 'Navigation',
    div: 'Box',
    ul: 'List',
    li: 'List item',
    a: 'Link',
    h1: 'Heading',
    h2: 'Heading',
    h3: 'Heading',
    h4: 'Heading',
    h5: 'Heading',
    h6: 'Heading',
    p: 'Paragraph',
}

function tagNameToReadableTagName(tagName: string) {
    return tagNamesToFullNameMapping[tagName] ?? tagName
}
