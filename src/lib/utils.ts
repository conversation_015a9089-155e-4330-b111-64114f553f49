import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs))
}

// Utility function to create a debounced function (currently unused but kept for future use)
export function debounce<T extends (...args: any[]) => any>(func: T, delay: number): T {
    let timeoutId: NodeJS.Timeout
    return ((...args: any[]) => {
        clearTimeout(timeoutId)
        timeoutId = setTimeout(() => func(...args), delay)
    }) as T
}

export function getElementNodePath(node: HTMLElement): number[] {
    if (!node.parentElement || node.tagName === 'BODY') {
        return []
    }

    const index = Array.from(node.parentElement.children).indexOf(node)
    return [...getElementNodePath(node.parentElement), index]
}
