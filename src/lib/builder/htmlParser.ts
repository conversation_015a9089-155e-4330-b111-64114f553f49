/**
 * HTML parsing utilities for the website builder
 */
import { HtmlNode } from '../types';

/**
 * Parses HTML content into a tree structure
 * @param htmlContent - The HTML content to parse
 * @returns The parsed HTML structure as a HtmlNode tree
 */
export const parseHtmlContent = (htmlContent: string): HtmlNode | null => {
  if (!htmlContent) return null;

  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlContent, 'text/html');

  // Parse the body element into a tree structure
  const htmlNode = parseNode(doc.body, 'root');
  return htmlNode;
};

/**
 * Recursively parses an Element into a HtmlNode
 * @param node - The Element to parse
 * @param idPrefix - Prefix for generating unique IDs
 * @returns The parsed HtmlNode
 */
export const parseNode = (node: Element, idPrefix: string): HtmlNode => {
  const id = `${idPrefix}-${Math.random().toString(36).substr(2, 9)}`;

  // Get attributes
  const attributes: Record<string, string> = {};
  for (let i = 0; i < node.attributes.length; i++) {
    const attr = node.attributes[i];
    attributes[attr.name] = attr.value;
  }

  // Parse children
  const children: HtmlNode[] = [];
  for (let i = 0; i < node.children.length; i++) {
    children.push(parseNode(node.children[i], id));
  }

  return {
    tagName: node.tagName.toLowerCase(),
    children,
    attributes,
    id
  };
};

/**
 * Updates the iframe content with the provided HTML
 * @param iframe - The iframe element
 * @param htmlContent - The HTML content to write to the iframe
 * @returns The iframe document or null if not available
 */
export const updateIframeContent = (
  iframe: HTMLIFrameElement,
  htmlContent: string
): Document | null => {
  if (!iframe) return null;

  const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;
  if (!iframeDocument) return null;

  // Write content to iframe
  iframeDocument.open();
  iframeDocument.write(htmlContent);
  iframeDocument.close();

  return iframeDocument;
};

/**
 * Adds CSS styles to the iframe document
 * @param iframeDocument - The iframe document
 * @param styles - CSS styles to add
 */
export const addStylesToIframe = (iframeDocument: Document, styles: string): void => {
  const style = iframeDocument.createElement('style');
  style.textContent = styles;
  iframeDocument.head.appendChild(style);
};

/**
 * Updates the iframe height based on content
 * @param iframe - The iframe element
 */
export const updateIframeHeight = (iframe: HTMLIFrameElement): void => {
  if (!iframe.contentDocument) return;

  const htmlElementHeight = iframe.contentDocument.documentElement.scrollHeight;
  iframe.style.height = `${htmlElementHeight}px`;
};
