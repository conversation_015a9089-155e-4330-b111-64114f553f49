import { debounce } from '@/lib/utils.ts'

export interface ViewerState {
    scale: number;
    translateX: number;
    translateY: number;
}

export interface ViewerConfig {
    minScale: number;
    maxScale: number;
    scaleStep: number;
    panSensitivity: number;
}

const DEFAULT_CONFIG: ViewerConfig = {
    minScale: 0.1,
    maxScale: 3,
    scaleStep: 0.01,
    panSensitivity: 1
}

export class Viewer {
    private state: ViewerState
    private config: ViewerConfig
    private isPanning = false
    private isSpacePressed = false
    private lastPanPoint: {x: number; y: number} | null = null
    private onTransformChange?: () => void

    constructor(
        private container: HTMLElement,
        private transformTarget: HTMLElement,
        private iframeTarget?: HTMLIFrameElement,
        config: Partial<ViewerConfig> = {},
        onTransformChange?: () => void
    ) {
        this.config = {...DEFAULT_CONFIG, ...config}
        this.onTransformChange = onTransformChange
        this.state = {
            scale: 1,
            translateX: 0,
            translateY: 0
        }

        this.setupEventListeners()
        this.updateTransform()
    }

    deactivateInteractionOverlay = debounce(() => {
        if (!this) {
            return
        }
        this.container.style.pointerEvents = 'auto'
    }, 50)

    private setupEventListeners(): void {
        // Handle wheel events for panning and zooming
        this.container.addEventListener('wheel', this.handleWheel.bind(this), {passive: false})
        this.iframeTarget?.contentDocument?.addEventListener('wheel', this.handleWheel.bind(this), {passive: false})

        // Handle keyboard events for spacebar panning
        document.addEventListener('keydown', this.handleKeyDown.bind(this))
        document.addEventListener('keyup', this.handleKeyUp.bind(this))

        // Handle mouse events for spacebar + drag panning
        this.container.addEventListener('mousedown', this.handleMouseDown.bind(this))
        this.container.addEventListener('mousemove', this.handleMouseMove.bind(this))
        this.container.addEventListener('mouseup', this.handleMouseUp.bind(this))
        this.container.addEventListener('mouseleave', this.handleMouseLeave.bind(this))

        // Prevent context menu when space is pressed
        this.container.addEventListener('contextmenu', this.handleContextMenu.bind(this))

        // this.container.addEventListener('mousemove', (event) => {
        //     this.iframeTarget?.contentWindow?.postMessage({
        //         key: 'forwarded-mousemove',
        //         type: event.type,
        //         clientX: event.clientX,
        //         clientY: event.clientY,
        //         screenX: event.screenX,
        //         screenY: event.screenY,
        //     }, '*') // Replace * with your iframe origin if known
        // })
        //
        // this.container.addEventListener('click', (event) => {
        //     this.iframeTarget?.contentWindow?.postMessage({
        //         key: 'forwarded-mousemove',
        //         type: event.type,
        //         clientX: event.clientX,
        //         clientY: event.clientY,
        //         screenX: event.screenX,
        //         screenY: event.screenY,
        //     }, '*') // Replace * with your iframe origin if known
        // })

        // this.iframeTarget?.contentWindow?.addEventListener('message', (e) => {
        //     if (e.data?.key === 'forwarded-mousemove') {
        //         const {clientX, clientY, screenX, screenY, type} = e.data
        //
        //         // Find the real element under this point
        //         const target = this.iframeTarget?.contentDocument?.elementFromPoint(clientX, clientY)
        //
        //         const syntheticEvent = new MouseEvent(type, {
        //             clientX,
        //             clientY,
        //             screenX,
        //             screenY,
        //             bubbles: true,
        //             cancelable: true,
        //             view: this.iframeTarget?.contentWindow
        //         })
        //
        //         if (target) {
        //             // Dispatch on a specific element if needed
        //             // this.iframeTarget?.contentDocument?.dispatchEvent(syntheticEvent)
        //             target?.dispatchEvent(syntheticEvent)
        //         }
        //     }
        // })
    }

    private handleWheel(event: WheelEvent): void {
        // this.container.style.pointerEvents = 'auto'

        event.preventDefault()
        event.stopPropagation()

        const isCommandPressed = event.metaKey || event.ctrlKey

        if (isCommandPressed) {
            // Zoom with Command/Ctrl + wheel
            this.handleZoom(event)
        } else {
            // Pan with trackpad or wheel
            this.handlePan(event)
        }
    }

    private handleZoom(event: WheelEvent): void {
        event.preventDefault()

        // Get mouse position relative to the container
        // Need to handle coordinates differently if event comes from iframe
        const containerRect = this.container.getBoundingClientRect()
        let mouseX: number
        let mouseY: number

        if (event.target && this.iframeTarget?.contentDocument?.contains(event.target as Node)) {
            // Event is from iframe - need to account for iframe's current transform
            // The iframe is inside the transformTarget which has: scale(scale) translate(translateX, translateY)
            // Event coordinates are relative to the iframe's viewport, but the iframe itself is transformed

            // Convert iframe coordinates to container coordinates by applying the current transform
            // Since transform-origin is top-left, the formula is:
            // containerCoord = iframeCoord * scale + translate * scale
            mouseX = event.clientX * this.state.scale + this.state.translateX * this.state.scale
            mouseY = event.clientY * this.state.scale + this.state.translateY * this.state.scale
        } else {
            // Event is from container - use coordinates directly
            mouseX = event.clientX - containerRect.left
            mouseY = event.clientY - containerRect.top
        }

        const prevScale = this.state.scale
        const delta = -event.deltaY
        const scaleFactor = (delta > 0 ? (1 + this.config.scaleStep) : (1 - this.config.scaleStep))
        const newScale = Math.max(this.config.minScale, Math.min(this.config.maxScale, prevScale * scaleFactor))

        if (newScale === prevScale) return

        // For transform-origin: top left, we need to:
        // 1. Find the point in the content that's currently under the mouse
        // 2. Adjust translation so that same point stays under the mouse after scaling

        // Current transform: scale(prevScale) translate(translateX, translateY)
        // Point under mouse in original content coordinates:
        const contentX = (mouseX - this.state.translateX * prevScale) / prevScale
        const contentY = (mouseY - this.state.translateY * prevScale) / prevScale

        // After scaling, we want the same content point to be under the mouse
        // New transform: scale(newScale) translate(newTranslateX, newTranslateY)
        // So: mouseX = contentX * newScale + newTranslateX * newScale
        // Therefore: newTranslateX = (mouseX - contentX * newScale) / newScale

        this.state.scale = newScale
        this.state.translateX = (mouseX - contentX * newScale) / newScale
        this.state.translateY = (mouseY - contentY * newScale) / newScale

        this.updateTransform()
    }

    private handlePan(event: WheelEvent): void {
        // this.container.style.pointerEvents = 'auto'
        // Handle 2D scrolling from trackpad or regular mouse wheel
        const deltaX = event.deltaX * this.config.panSensitivity
        const deltaY = event.deltaY * this.config.panSensitivity

        this.state.translateX -= deltaX
        this.state.translateY -= deltaY

        this.updateTransform()
    }

    private handleKeyDown(event: KeyboardEvent): void {
        if (event.code === 'Space' && !this.isSpacePressed) {
            this.isSpacePressed = true
            this.container.style.cursor = 'grab'
            event.preventDefault()
        }
    }

    private handleKeyUp(event: KeyboardEvent): void {
        if (event.code === 'Space') {
            this.isSpacePressed = false
            this.isPanning = false
            this.lastPanPoint = null
            this.container.style.cursor = ''
        }
    }

    private handleMouseDown(event: MouseEvent): void {
        if (this.isSpacePressed && event.button === 0) {
            this.isPanning = true
            this.lastPanPoint = {x: event.clientX, y: event.clientY}
            this.container.style.cursor = 'grabbing'
            event.preventDefault()
        }
    }

    private handleMouseMove(event: MouseEvent): void {
        // if (!this.isSpacePressed) {
        //     this.container.style.pointerEvents = 'none'
        //     this.deactivateInteractionOverlay()
        // }

        if (this.isPanning && this.lastPanPoint) {
            const deltaX = event.clientX - this.lastPanPoint.x
            const deltaY = event.clientY - this.lastPanPoint.y

            this.state.translateX += deltaX
            this.state.translateY += deltaY

            this.lastPanPoint = {x: event.clientX, y: event.clientY}
            this.updateTransform()
        }
    }

    private handleMouseUp(event: MouseEvent): void {
        if (this.isPanning && event.button === 0) {
            this.isPanning = false
            this.lastPanPoint = null
            this.container.style.cursor = this.isSpacePressed ? 'grab' : ''
        }
    }

    private handleMouseLeave(): void {
        this.isPanning = false
        this.lastPanPoint = null
        if (this.isSpacePressed) {
            this.container.style.cursor = 'grab'
        }
    }

    private handleContextMenu(event: MouseEvent): void {
        if (this.isSpacePressed) {
            event.preventDefault()
        }
    }

    private updateTransform(): void {
        this.transformTarget.style.transformOrigin = 'top left'
        this.transformTarget.style.transform = `scale(${this.state.scale}) translate(${this.state.translateX}px, ${this.state.translateY}px)`
        this.onTransformChange?.()
    }

    // Public methods for external control
    public getState(): ViewerState {
        return {...this.state}
    }

    public setState(newState: Partial<ViewerState>): void {
        this.state = {...this.state, ...newState}
        this.updateTransform()
    }

    public resetView(): void {
        this.state = {
            scale: 1,
            translateX: 0,
            translateY: 0
        }
        this.updateTransform()
    }

    public fitToContainer(): void {
        // Reset to default view
        this.resetView()
    }

    public focusOnElement(element: HTMLElement, padding: number = 50): void {
        if (!this.iframeTarget || !this.iframeTarget.contentDocument) {
            console.warn('Cannot focus on element: iframe not available')
            return
        }

        // Get container dimensions
        const containerRect = this.container.getBoundingClientRect()
        const containerWidth = containerRect.width
        const containerHeight = containerRect.height

        // Get the element's bounding box relative to the iframe
        const elementRect = element.getBoundingClientRect()
        const iframeRect = this.iframeTarget.getBoundingClientRect()

        // debugger
        // Calculate element position relative to iframe viewport (already transformed)
        const transformedElementX = elementRect.left
        const transformedElementY = elementRect.top

        // Check if element is already reasonably visible in the current viewport
        const viewportPadding = 100 // How much of the element should be visible to consider it "in view"
        const elementRight = transformedElementX + elementRect.width
        const elementBottom = transformedElementY + elementRect.height

        const isElementVisible = (
            transformedElementX > -viewportPadding &&
            transformedElementY > -viewportPadding &&
            elementRight < containerWidth + viewportPadding &&
            elementBottom < containerHeight + viewportPadding
        )

        // If element is already visible, don't focus
        if (isElementVisible) {
            console.log('Element is already visible, skipping focus')
            return
        }

        // Convert from current transformed coordinates to original coordinates
        // Current transform: scale(scale) translate(translateX, translateY)
        // To get original position: (transformed_pos / current_scale) - current_translate
        const originalElementX = (transformedElementX / this.state.scale) - this.state.translateX
        const originalElementY = (transformedElementY / this.state.scale) - this.state.translateY

        // Calculate the original element dimensions (without current scale)
        const originalElementWidth = elementRect.width / this.state.scale
        const originalElementHeight = elementRect.height / this.state.scale

        // Calculate the scale needed to fit the element with padding
        const availableWidth = containerWidth - (padding * 2)
        const availableHeight = containerHeight - (padding * 2)

        const scaleX = availableWidth / originalElementWidth
        const scaleY = availableHeight / originalElementHeight
        const targetScale = Math.min(scaleX, scaleY, this.config.maxScale)

        // Ensure we don't go below minimum scale, but also don't zoom out too much from current scale
        const minScaleForFocus = Math.min(this.state.scale * 0.5, this.config.minScale)
        const newScale = Math.max(targetScale, minScaleForFocus)

        // Calculate the center point of the element in original coordinates
        const elementCenterX = originalElementX + originalElementWidth / 2
        const elementCenterY = originalElementY + originalElementHeight / 2

        // Calculate the center point of the container
        const containerCenterX = containerWidth / 2
        const containerCenterY = containerHeight / 2

        // Calculate translation needed to center the element
        const newTranslateX = (containerCenterX - elementCenterX * newScale) / newScale
        const newTranslateY = (containerCenterY - elementCenterY * newScale) / newScale

        // Apply the new state
        this.setState({
            scale: newScale,
            translateX: newTranslateX,
            translateY: newTranslateY
        })
    }

    public centerAndZoomOut(zoomOutFactor: number = 0.8): void {
        if (!this.iframeTarget) {
            console.warn('Cannot center: iframe not available')
            return
        }

        // Get container and iframe dimensions
        const containerRect = this.container.getBoundingClientRect()
        const iframeRect = this.iframeTarget.getBoundingClientRect()

        const containerWidth = containerRect.width
        const containerHeight = containerRect.height
        const iframeWidth = this.iframeTarget.offsetWidth
        const iframeHeight = this.iframeTarget.offsetHeight

        // Calculate scale to fit iframe in container with zoom out factor
        const scaleX = (containerWidth * zoomOutFactor) / iframeWidth
        const scaleY = (containerHeight * zoomOutFactor) / iframeHeight
        // const targetScale = Math.min(scaleX, scaleY, this.config.maxScale)
        const targetScale = zoomOutFactor

        // Ensure we don't go below minimum scale
        const newScale = Math.max(targetScale, this.config.minScale)

        // Calculate translation to center horizontally and focus on top section vertically
        const scaledIframeWidth = iframeWidth * newScale
        const scaledIframeHeight = iframeHeight * newScale

        // Center horizontally
        const newTranslateX = (containerWidth - scaledIframeWidth) / (2 * newScale)

        // Focus on top section - position iframe so top is visible with some padding
        const topPadding = 50 // pixels of padding from the top
        const newTranslateY = topPadding / newScale

        // Apply the new state
        this.setState({
            scale: newScale,
            translateX: newTranslateX,
            translateY: newTranslateY
        })
    }

    public zoomIn(): void {
        const newScale = Math.min(this.config.maxScale, this.state.scale * (1 + this.config.scaleStep))
        this.setState({scale: newScale})
    }

    public zoomOut(): void {
        const newScale = Math.max(this.config.minScale, this.state.scale * (1 - this.config.scaleStep))
        this.setState({scale: newScale})
    }

    public destroy(): void {
        this.container.removeEventListener('wheel', this.handleWheel.bind(this))
        this.iframeTarget?.contentDocument?.removeEventListener('wheel', this.handleWheel.bind(this))
        document.removeEventListener('keydown', this.handleKeyDown.bind(this))
        document.removeEventListener('keyup', this.handleKeyUp.bind(this))
        this.container.removeEventListener('mousedown', this.handleMouseDown.bind(this))
        this.container.removeEventListener('mousemove', this.handleMouseMove.bind(this))
        this.container.removeEventListener('mouseup', this.handleMouseUp.bind(this))
        this.container.removeEventListener('mouseleave', this.handleMouseLeave.bind(this))
        this.container.removeEventListener('contextmenu', this.handleContextMenu.bind(this))
    }
}
