/**
 * Canvas overlay utilities for visualizing element margins, padding, and hover states
 */

import { updateIframeHeight } from '@/lib/builder/htmlParser.ts'
import { ViewerState } from './viewer'

/**
 * Draws an overlay on the canvas to visualize element margins and padding
 * @param element - The element to visualize
 * @param canvas - The canvas element to draw on
 * @param iframe - The iframe containing the element
 * @param viewerState - The current viewer transform state (optional)
 */
export const drawOverlay = (
  element: HTMLElement | null,
  canvas: HTMLCanvasElement,
  iframe: HTMLIFrameElement,
  viewerState?: ViewerState
): void => {
  if (!element || !iframe || !canvas) {
    // Clear canvas if no element is selected or refs are not ready
    clearCanvas(canvas);
    return;
  }

  const iframeWindow = iframe.contentWindow;
  if (!iframeWindow) {
    clearCanvas(canvas);
    return;
  }

  const ctx = canvas.getContext('2d');
  if (!ctx) {
    clearCanvas(canvas);
    return;
  }

  const rect = element.getBoundingClientRect();
  const computedStyle = iframeWindow.getComputedStyle(element);

  // Parse pixel values
  const marginTop = pxToNum(computedStyle.marginTop);
  const marginRight = pxToNum(computedStyle.marginRight);
  const marginBottom = pxToNum(computedStyle.marginBottom);
  const marginLeft = pxToNum(computedStyle.marginLeft);

  // Get padding values
  const paddingTop = pxToNum(computedStyle.paddingTop);
  const paddingRight = pxToNum(computedStyle.paddingRight);
  const paddingBottom = pxToNum(computedStyle.paddingBottom);
  const paddingLeft = pxToNum(computedStyle.paddingLeft);

  // Apply viewer transformation if provided - simplified since canvas is now inside transformed container
  const scale = viewerState?.scale || 1;
  const translateX = viewerState?.translateX || 0;
  const translateY = viewerState?.translateY || 0;

  // Calculate canvas position relative to the iframe (no need for complex transform math)
  const canvasTop = rect.top - marginTop;
  const canvasLeft = rect.left - marginLeft;

  // Calculate canvas dimensions including margins (no scaling needed since parent is scaled)
  const canvasWidth = rect.width + marginLeft + marginRight;
  const canvasHeight = rect.height + marginTop + marginBottom;

  // Set canvas style for positioning and size
  canvas.style.position = 'absolute';
  canvas.style.top = `${canvasTop}px`;
  canvas.style.left = `${canvasLeft}px`;
  canvas.style.width = `${canvasWidth}px`;
  canvas.style.height = `${canvasHeight}px`;
  canvas.style.pointerEvents = 'none'; // Make canvas non-interactive
  canvas.style.zIndex = '10'; // Ensure canvas is on top
  canvas.style.display = 'block'; // Show canvas
  canvas.style.outline = '2px solid rgb(151 226 255)'; // Show canvas
  canvas.style.border = '1px solid rgb(0, 184, 255)'; // Show canvas
  canvas.style.borderRadius = '1px'; // Show canvas

  // Set canvas drawing buffer size (important for clear rendering)
  canvas.width = canvasWidth;
  canvas.height = canvasHeight;

  // Clear previous drawings
  ctx.clearRect(0, 0, canvasWidth, canvasHeight);

  // Draw margin rectangles (semi-transparent orange)
  ctx.fillStyle = 'rgba(255, 165, 0, 0.4)'; // Orange with alpha

  // Top margin
  if (marginTop > 0) {
    ctx.fillRect(0, 0, rect.width + marginLeft + marginRight, marginTop);
  }
  // Bottom margin
  if (marginBottom > 0) {
    ctx.fillRect(0, marginTop + rect.height, rect.width + marginLeft + marginRight, marginBottom);
  }
  // Left margin
  if (marginLeft > 0) {
    ctx.fillRect(0, marginTop, marginLeft, rect.height);
  }
  // Right margin
  if (marginRight > 0) {
    ctx.fillRect(marginLeft + rect.width, marginTop, marginRight, rect.height);
  }

  // Draw padding rectangles (semi-transparent blue)
  ctx.fillStyle = 'rgba(173, 216, 230, 0.5)'; // Light blue with alpha

  // Element's inner position (relative to canvas origin)
  const elementInnerX = marginLeft;
  const elementInnerY = marginTop;

  // Top padding
  if (paddingTop > 0) {
    ctx.fillRect(elementInnerX, elementInnerY, rect.width, paddingTop);
  }
  // Bottom padding
  if (paddingBottom > 0) {
    ctx.fillRect(elementInnerX, elementInnerY + rect.height - paddingBottom, rect.width, paddingBottom);
  }
  // Left padding
  if (paddingLeft > 0) {
    // Draw between top and bottom padding areas
    ctx.fillRect(elementInnerX, elementInnerY + paddingTop, paddingLeft, rect.height - paddingTop - paddingBottom);
  }
  // Right padding
  if (paddingRight > 0) {
    // Draw between top and bottom padding areas
    ctx.fillRect(elementInnerX + rect.width - paddingRight, elementInnerY + paddingTop, paddingRight, rect.height - paddingTop - paddingBottom);
  }
};

/**
 * Clears the canvas and hides it
 * @param canvas - The canvas element to clear
 */
export const clearCanvas = (canvas: HTMLCanvasElement): void => {
  const ctx = canvas.getContext('2d');
  if (ctx) {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    canvas.style.display = 'none'; // Hide canvas
  }
};

/**
 * Helper function to parse pixel values (e.g., "10px")
 * @param val - The pixel value string
 * @returns The numeric value
 */
export const pxToNum = (val: string | null): number => val ? parseFloat(val) : 0;

/**
 * Draws a hover overlay on the canvas to visualize the hovered element
 * @param element - The element to visualize
 * @param canvas - The canvas element to draw on
 * @param iframe - The iframe containing the element
 * @param viewerState - The current viewer transform state (optional)
 */
export const drawHoverOverlay = (
  element: HTMLElement | undefined,
  canvas: HTMLCanvasElement,
  iframe: HTMLIFrameElement,
  viewerState?: ViewerState
): void => {
  if (!element || !iframe || !canvas) {
    // Clear canvas if no element is hovered or refs are not ready
    clearCanvas(canvas);
    return;
  }

  const iframeWindow = iframe.contentWindow;
  if (!iframeWindow || element.tagName === 'HTML') {
    clearCanvas(canvas);
    return;
  }

  const ctx = canvas.getContext('2d');
  if (!ctx) {
    clearCanvas(canvas);
    return;
  }

  const rect = element.getBoundingClientRect();
  const computedStyle = iframeWindow.getComputedStyle(element);

  // Parse pixel values
  const marginTop = pxToNum(computedStyle.marginTop);
  const marginRight = pxToNum(computedStyle.marginRight);
  const marginBottom = pxToNum(computedStyle.marginBottom);
  const marginLeft = pxToNum(computedStyle.marginLeft);

  // Apply viewer transformation if provided - simplified since canvas is now inside transformed container
  const scale = viewerState?.scale || 1;
  const translateX = viewerState?.translateX || 0;
  const translateY = viewerState?.translateY || 0;

  // Calculate canvas position relative to the iframe (no need for complex transform math)
  const canvasTop = rect.top - marginTop;
  const canvasLeft = rect.left - marginLeft;

  // Calculate canvas dimensions including margins (no scaling needed since parent is scaled)
  const canvasWidth = rect.width + marginLeft + marginRight;
  const canvasHeight = rect.height + marginTop + marginBottom;

  // Set canvas style for positioning and size
  canvas.style.top = `${canvasTop}px`;
  canvas.style.left = `${canvasLeft}px`;
  canvas.style.width = `${canvasWidth}px`;
  canvas.style.height = `${canvasHeight}px`;
  canvas.style.display = `block`;

  // Set canvas drawing buffer size (important for clear rendering)
  canvas.width = canvasWidth;
  canvas.height = canvasHeight;
  // Clear previous drawings
  // ctx.clearRect(0, 0, canvasWidth, canvasHeight);

  // Draw a semi-transparent blue border around the element (including margins)
  // ctx.strokeStyle = 'rgb(102,174,255)';
  // ctx.lineWidth = 6;
  //
  // // Draw the border at the element's boundaries (not including margins)
  // ctx.strokeRect(
  //   marginLeft,     // X position (offset by left margin)
  //   marginTop,      // Y position (offset by top margin)
  //     canvasWidth,     // Width of the element
  //     canvasHeight // Height of the element
  // );
};

export const setupOverlayEventListeners = (
  element: HTMLElement,
  canvas: HTMLCanvasElement,
  iframe: HTMLIFrameElement,
  getViewerState?: () => ViewerState | undefined
): () => void => {
  const iframeWindow = iframe.contentWindow;
  if (!iframeWindow) return () => {};

  // Function to redraw the overlay
  const redraw = () => {
    const viewerState = getViewerState ? getViewerState() : undefined;
    drawOverlay(element, canvas, iframe, viewerState);

    // Also, update iframe height in case content reflowed
    updateIframeHeight(iframe);
  };

  const iframeScrollListener = () => redraw();
  const windowResizeListener = () => redraw();

  iframeWindow.addEventListener('scroll', iframeScrollListener);
  window.addEventListener('resize', windowResizeListener);

  // Return cleanup function
  return () => {
    iframeWindow.removeEventListener('scroll', iframeScrollListener);
    window.removeEventListener('resize', windowResizeListener);
  };
};

/**
 * Gets the element at a specific position in the iframe
 * @param x - The x coordinate relative to the iframe
 * @param y - The y coordinate relative to the iframe
 * @param iframe - The iframe to get the element from
 * @returns The element at the specified position or null if none found
 */
export const getElementAtPosition = (
  x: number,
  y: number,
  iframe: HTMLIFrameElement
): HTMLElement | undefined => {
  if (!iframe || !iframe.contentDocument) return;

  const iframeDocument = iframe.contentDocument;
  const element = iframeDocument.elementFromPoint(x, y) as HTMLElement | undefined;

  return element ?? undefined;
};

/**
 * Sets up event listeners for redrawing the hover overlay on scroll and resize
 * @param element - The element to visualize
 * @param canvas - The canvas element
 * @param iframe - The iframe containing the element
 * @param getViewerState - Function to get the current viewer state
 * @returns A cleanup function to remove the event listeners
 */
export const setupHoverOverlayEventListeners = (
  element: HTMLElement,
  canvas: HTMLCanvasElement,
  iframe: HTMLIFrameElement,
  getViewerState?: () => ViewerState | undefined
): () => void => {
  const iframeWindow = iframe.contentWindow;
  if (!iframeWindow) return () => {};

  // Function to redraw the hover overlay
  const redraw = () => {
    const viewerState = getViewerState ? getViewerState() : undefined;
    drawHoverOverlay(element, canvas, iframe, viewerState);
  };

  // const iframeScrollListener = () => redraw();
  const windowResizeListener = () => redraw();

  // iframeWindow.addEventListener('scroll', iframeScrollListener);
  // iframeWindow.addEventListener('wheel', ()=> console.log(22));
  window.addEventListener('resize', windowResizeListener);

  // Return cleanup function
  return () => {
    // iframeWindow.removeEventListener('scroll', iframeScrollListener);
    window.removeEventListener('resize', windowResizeListener);
  };
};
