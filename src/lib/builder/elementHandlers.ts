/**
 * Element interaction handlers for the website builder
 * These functions handle element selection, hover effects, etc.
 */

/**
 * Creates a click handler for an element
 * @param element - The HTML element to handle clicks for
 * @param onElementSelect - Callback function to call when an element is selected
 */
export const createElementClickHandler = (
  element: HTMLElement,
  onElementSelect: (element: HTMLElement) => void
) => (e: MouseEvent) => {
  e.stopPropagation();
  e.preventDefault();
  onElementSelect(element);
};

/**
 * Makes the selected element draggable
 * @param element - The element to make draggable
 * @returns A function to handle the dragstart event
 */
export const makeElementDraggable = (element: HTMLElement) => {
  // Make the element draggable
  element.setAttribute('draggable', 'true');

  // Return the dragstart handler
  return (e: DragEvent) => {
    e.stopPropagation();

    // Set data to identify this as a move operation (not a new component)
    e.dataTransfer?.setData('moveElement', 'true');

    // Store the element's ID or create one if it doesn't exist
    if (!element.id) {
      element.id = `element-${Date.now()}`;
    }

    e.dataTransfer?.setData('elementId', element.id);
  };
};

/**
 * Removes event handlers from all elements
 * @param eventListenersMap - Map containing event listeners
 */
export const removeEventHandlers = (
  eventListenersMap: Map<HTMLElement, {
    click: (e: MouseEvent) => void;
    dblclick?: (e: MouseEvent) => void;
    dragstart?: (e: DragEvent) => void;
    // mouseenter: (e: MouseEvent) => void;
    // mouseleave: (e: MouseEvent) => void;
  }>
) => {
  eventListenersMap.forEach((handlers, element) => {
    element.removeEventListener('click', handlers.click);
    if (handlers.dblclick) {
      element.removeEventListener('dblclick', handlers.dblclick);
    }
    if (handlers.dragstart) {
      element.removeEventListener('dragstart', handlers.dragstart as EventListener);
      // Reset draggable attribute
      element.setAttribute('draggable', 'false');
    }
    // element.removeEventListener('mouseenter', handlers.mouseenter);
    // element.removeEventListener('mouseleave', handlers.mouseleave);
  });

  // Clear the map
  eventListenersMap.clear();
};

/**
 * Clones an element and all its properties, styles, and content
 * @param element - The element to clone
 * @returns The cloned element
 */
export const cloneElement = (element: HTMLElement): HTMLElement => {
  // Clone the element deeply (including all children and attributes)
  const clonedElement = element.cloneNode(true) as HTMLElement;

  // Generate a new unique ID for the cloned element to avoid conflicts
  if (clonedElement.id) {
    clonedElement.id = `${clonedElement.id}-copy-${Date.now()}`;
  } else {
    clonedElement.id = `element-copy-${Date.now()}`;
  }

  // Also update IDs of any child elements to avoid conflicts
  const updateChildIds = (el: HTMLElement) => {
    const children = el.querySelectorAll('[id]');
    children.forEach((child) => {
      if (child.id) {
        child.id = `${child.id}-copy-${Date.now()}`;
      }
    });
  };

  updateChildIds(clonedElement);

  return clonedElement;
};

/**
 * Handles keydown events for element deletion and copy-paste
 * @param e - The keyboard event
 * @param selectedElement - The currently selected element
 * @param iframeRef - Reference to the iframe
 * @param onHtmlContentChange - Callback to update HTML content
 * @param onElementSelect - Callback to update selected element
 * @param onStyleUpdate - Callback to update styles
 * @param setupElementEventHandlers - Callback to setup event handlers for new elements
 */
export const handleKeyDown = (
  e: KeyboardEvent,
  selectedElement: HTMLElement  | undefined,
  iframeRef: { current: HTMLIFrameElement | null },
  onHtmlContentChange: (content: string) => void,
  onElementSelect: (element?: HTMLElement) => void,
  onStyleUpdate: () => void,
) => {
  // Handle Delete key for element deletion
  if (e.key === 'Delete' && selectedElement) {
    e.preventDefault();

    // Remove the element from DOM
    selectedElement.remove();

    // Update the HTML content from iframe
    if (iframeRef.current?.contentDocument) {
      const updatedHtmlContent = iframeRef.current.contentDocument.documentElement.outerHTML;
      onHtmlContentChange(updatedHtmlContent);
    }

    // Clear selected element reference
    onElementSelect(undefined);

    // Optionally save changes
    onStyleUpdate();
  }

  // Handle ESC for element deselection
  if (e.key === 'Escape' && selectedElement) {
    e.preventDefault();
    onElementSelect(undefined);
  }

  // Handle CTRL/CMD+D for copy-paste functionality
  if (e.key === 'd' && (e.ctrlKey || e.metaKey) && selectedElement) {
    e.preventDefault();

    const iframeDocument = iframeRef.current?.contentDocument;
    if (!iframeDocument) return;

    // Clone the selected element
    const clonedElement = cloneElement(selectedElement);

    // Find the parent of the selected element
    const parentElement = selectedElement.parentElement;
    if (!parentElement) return;

    // Insert the cloned element right after the selected element
    const nextSibling = selectedElement.nextSibling;
    if (nextSibling) {
      parentElement.insertBefore(clonedElement, nextSibling);
    } else {
      parentElement.appendChild(clonedElement);
    }

    // Update the HTML content from iframe
    const updatedHtmlContent = iframeDocument.documentElement.outerHTML;
    onHtmlContentChange(updatedHtmlContent);

    // Select the newly cloned element
    onElementSelect(clonedElement);

    // Save changes
    onStyleUpdate();
  }
};
