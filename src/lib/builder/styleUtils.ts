/**
 * Utilities for working with CSS styles in the website builder
 */
export type BoxShadowPositionValue = 'outset' | 'inset' | '';

/**
 * Gets the original CSS value from an element's style
 * This returns the value as it was defined (e.g., "100%", "2em") rather than computed values
 *
 * @param element - The HTML element to get the style from
 * @param property - The CSS property name (in camelCase)
 * @returns The original CSS value or empty string if not found
 */
export const getOriginalStyleValue = (
    element: HTMLElement | null,
    property: string
): string => {
    if (!element) return ''

    // First check inline styles (highest priority)
    const inlineValue = element.style[property as any]
    if (inlineValue) return inlineValue

    // Then check all stylesheets
    const stylesheets = Array.from(document.styleSheets)

    try {
        // Filter to only same-origin stylesheets we can access
        const accessibleStylesheets = stylesheets.filter(sheet => {
            try {
                // This will throw if the stylesheet is cross-origin
                // @ts-expect-error We need this line because an error is thrown otherwise
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                const rules = sheet.cssRules
                return true

                // eslint-disable-next-line
            } catch (e) {
                return false
            }
        })

        // Convert property from camelCase to kebab-case for CSS rules
        const kebabProperty = property.replace(/([A-Z])/g, '-$1').toLowerCase()

        // Check each stylesheet
        for(const sheet of accessibleStylesheets) {
            const rules = Array.from(sheet.cssRules)

            // Check each rule
            for(const rule of rules) {
                if (rule instanceof CSSStyleRule) {
                    // Check if the element matches this selector
                    if (element.matches(rule.selectorText)) {
                        const value = rule.style.getPropertyValue(kebabProperty)
                        if (value) return value
                    }
                }
            }
        }
    } catch (error) {
        console.error('Error accessing stylesheets:', error)
    }

    // If we couldn't find the original value, return empty string
    return ''
}

/**
 * Gets the style value for a property, trying original value first, then computed
 *
 * @param element - The HTML element to get the style from
 * @param property - The CSS property name (in camelCase)
 * @param computedStyles - Optional pre-computed styles object
 * @returns The CSS value (original if available, computed as fallback)
 */
export const getStyleValue = (
    element: HTMLElement | null,
    property: string,
    computedStyles?: CSSStyleDeclaration
): string => {
    if (!element) return ''

    // Try to get the original value first
    const originalValue = getOriginalStyleValue(element, property)
    if (originalValue) return originalValue

    // Fall back to computed value
    const styles = computedStyles || window.getComputedStyle(element)
    return styles[property as any] || ''
}

/**
 * Gets dimension style (width, height) with preference for percentage values
 *
 * @param element - The HTML element to get the style from
 * @param property - The dimension property ('width' or 'height')
 * @returns The original dimension value (like '100%') or computed value as fallback
 */
export const getDimensionStyle = (
    element: HTMLElement | null,
    property: 'width' | 'height' | 'maxWidth' | 'maxHeight'
): string => {
    if (!element) return ''

    // First check inline style
    const inlineValue = element.style[property]
    if (inlineValue) return inlineValue

    // Then check style attribute for a percentage value
    const styleAttr = element.getAttribute('style')
    if (styleAttr) {
        const percentMatch = new RegExp(`${property}:\\s*([\\d.]+%)`, 'i').exec(styleAttr)
        if (percentMatch && percentMatch[1]) {
            return percentMatch[1]
        }
    }

    // Check if the element has a parent with a layout that might affect percentage calculation
    const parent = element.parentElement
    if (parent) {
        const parentStyles = window.getComputedStyle(parent)
        const elementStyles = window.getComputedStyle(element)

        // If parent has a defined width/height and element takes up most of it,
        // it might be using a percentage
        if (property === 'width') {
            const parentWidth = parseFloat(parentStyles.width)
            const elementWidth = parseFloat(elementStyles.width)

            // Check if element width is close to 100%, 50%, 25%, 75% of parent
            const percentage = Math.round((elementWidth / parentWidth) * 100)
            if ([25, 33, 50, 66, 75, 100].includes(percentage)) {
                return `${percentage}%`
            }
        } else if (property === 'height') {
            const parentHeight = parseFloat(parentStyles.height)
            const elementHeight = parseFloat(elementStyles.height)

            // Check if element height is close to 100%, 50%, 25%, 75% of parent
            const percentage = Math.round((elementHeight / parentHeight) * 100)
            if ([25, 33, 50, 66, 75, 100].includes(percentage)) {
                return `${percentage}%`
            }
        }
    }

    // Fall back to computed value
    return window.getComputedStyle(element)[property]
}
// Utility function to parse box-shadow CSS value
export const parseBoxShadow = (boxShadowValue: string) => {
    if (!boxShadowValue || boxShadowValue === 'none') {
        return {
            offsetX: 0,
            offsetY: 0,
            blur: 0,
            spread: 0,
            color: '',
            position: 'outset' as BoxShadowPositionValue
        }
    }

    // Parse box-shadow (handles colors at beginning, middle, or end)
    // Format: [inset] [color] <offset-x> <offset-y> <blur-radius> <spread-radius> [color]
    const isInset = boxShadowValue.includes('inset')
    const cleanValue = boxShadowValue.replace(/\binset\b/, '').trim()

    // Extract color (look for rgb, rgba, hex, hsl, hsla, or named colors anywhere in the string)
    const colorMatch = cleanValue.match(/(rgba?\([^)]+\)|hsla?\([^)]+\)|#[0-9a-fA-F]{3,8}|[a-zA-Z]+(?:\s+[a-zA-Z]+)*)/g)
    let color = ''

    // Find the first valid color match (filter out common non-color words)
    if (colorMatch) {
        const nonColorWords = ['inset', 'px', 'em', 'rem', '%', 'auto', 'inherit', 'initial', 'unset']
        color = colorMatch.find(match =>
            !nonColorWords.includes(match.toLowerCase()) &&
            !match.match(/^-?\d*\.?\d+(px|em|rem|%)?$/)
        ) || ''
    }

    // Remove the color from the string to extract numeric values
    const withoutColor = color ? cleanValue.replace(color, '').trim() : cleanValue

    // Extract numeric values (px, em, rem, etc.)
    const values = withoutColor.match(/(-?\d*\.?\d+)(px|em|rem|%)?/g) || []

    return {
        offsetX: values[0] ? parseFloat(values[0]) : 0,
        offsetY: values[1] ? parseFloat(values[1]) : 0,
        blur: values[2] ? parseFloat(values[2]) : 0,
        spread: values[3] ? parseFloat(values[3]) : 0,
        color: color,
        position: isInset ? 'inset' as BoxShadowPositionValue : 'outset' as BoxShadowPositionValue
    }
}
