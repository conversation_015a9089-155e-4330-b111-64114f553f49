/**
 * Drag and drop utilities for the website builder
 */

/**
 * Checks if an element can contain other elements
 * @param element - The element to check
 * @returns True if the element can contain other elements
 */
const isContainer = (element: HTMLElement): boolean => {
    // List of tags that can contain other elements
    const containerTags = [
        'DIV', 'SECTION', 'ARTICLE', 'MAIN', 'ASIDE', 'HEADER', 'FOOTER',
        'NAV', 'FORM', 'FIELDSET', 'UL', 'OL', 'LI', 'TABLE', 'TR', 'TD',
        'TBODY', 'THEAD', 'TFOOT', 'FIGURE', 'FIGCAPTION', 'DETAILS', 'SUMMARY'
    ]

    return containerTags.includes(element.tagName)
}

/**
 * Handles dragover events for component placement
 * @param e - The drag event
 * @param iframe - The iframe reference
 * @param dragOverElemRef - Reference to the element being dragged over
 * @param dragPosition - Reference to the current drag position (above/below/inside)
 * @param dropIndicatorRef - Reference to the drop indicator element outside the iframe
 */
export const handleDragOver = (
    e: DragEvent,
    iframe: HTMLIFrameElement,
    dragOverElemRef: {current: HTMLElement | null},
    dragPosition: {current: 'above' | 'below' | 'inside'},
    dropIndicatorRef: {current: HTMLDivElement | null}
): void => {
    e.preventDefault()

    if (!iframe || !iframe.contentDocument || !dropIndicatorRef.current) return

    const iframeDocument = iframe.contentDocument
    const iframeWindow = iframe.contentWindow
    const dropIndicator = dropIndicatorRef.current

    if (!iframeWindow) return

    // Get iframe position and scroll offsets
    const iframeRect = iframe.getBoundingClientRect()
    const scrollX = iframeWindow.scrollX
    const scrollY = (iframe.parentNode as HTMLElement)?.scrollTop || 0

    // Get the element under the pointer
    const elemUnderPointer = iframeDocument.elementFromPoint(
        e.clientX,
        e.clientY
    ) as HTMLElement

    if (!elemUnderPointer) {
        // Hide the indicator if no element is under the pointer
        dropIndicator.style.display = 'none'
        return
    }

    // Store the element we're hovering over
    dragOverElemRef.current = elemUnderPointer

    // Get element dimensions
    const rect = elemUnderPointer.getBoundingClientRect()

    // Define drop zones: top 25%, middle 50%, bottom 25%
    const topZone = rect.top + rect.height * 0.25
    const bottomZone = rect.top + rect.height * 0.75

    // Determine drop position based on cursor location and if element is a container
    let newPosition: 'above' | 'below' | 'inside'

    if (e.clientY < topZone) {
        // Top 25% - drop above
        newPosition = 'above'
    } else if (e.clientY > bottomZone) {
        // Bottom 25% - drop below
        newPosition = 'below'
    } else if (isContainer(elemUnderPointer)) {
        // Middle 50% and element is a container - drop inside
        newPosition = 'inside'
    } else {
        // Middle 50% but not a container - default to below
        newPosition = 'below'
    }

    // Calculate the absolute position of the indicator (accounting for iframe scroll)
    const indicatorLeft =rect.left + scrollX
    let indicatorTop

    // Style the indicator based on drop position
    if (newPosition === 'above') {
        // Line at the top of the element
        indicatorTop =  rect.top + scrollY
        dropIndicator.style.height = '2px'
        dropIndicator.style.width = rect.width + 'px'
        dropIndicator.style.backgroundColor = '#3b82f6' // Blue
    } else if (newPosition === 'below') {
        // Line at the bottom of the element
        indicatorTop =  rect.top + rect.height + scrollY
        dropIndicator.style.height = '2px'
        dropIndicator.style.width = rect.width + 'px'
        dropIndicator.style.backgroundColor = '#3b82f6' // Blue
    } else { // inside
        // Box around the entire element
        indicatorTop =  rect.top + scrollY
        dropIndicator.style.height = rect.height + 'px'
        dropIndicator.style.width = rect.width + 'px'
        dropIndicator.style.backgroundColor = 'rgba(59, 130, 246, 0.2)' // Transparent blue
        dropIndicator.style.border = '2px dashed #3b82f6'
    }

    // console.log('scroll ->', elemUnderPointer, iframeRect.top , rect.top , newPosition === 'below' ? rect.height: 0, scrollY, ' = ', indicatorTop)
    // Update the indicator position and make it visible
    dropIndicator.style.display = 'block'
    dropIndicator.style.left = indicatorLeft + 'px'
    dropIndicator.style.top = indicatorTop + 'px'

    console.log('indicator ->', iframeRect.top , rect.top , scrollY)

    // Update the current position
    dragPosition.current = newPosition
}

/**
 * Handles drop events for component placement or element movement
 * @param e - The drag event
 * @param iframe - The iframe reference
 * @param dragOverElemRef - Reference to the element being dragged over
 * @param dragPosition - Reference to the current drag position (above/below/inside)
 * @param onHtmlContentChange - Callback to update HTML content
 * @param setupElementEventHandlers - Function to set up event handlers for the new element
 * @param dropIndicatorRef - Reference to the drop indicator element outside the iframe
 * @returns The newly created or moved element or null if operation failed
 */
export const handleDrop = (
    e: DragEvent,
    iframe: HTMLIFrameElement,
    dragOverElemRef: {current: HTMLElement | null},
    dragPosition: {current: 'above' | 'below' | 'inside'},
    onHtmlContentChange: (content: string) => void,
    dropIndicatorRef: {current: HTMLDivElement | null}
): HTMLElement | null => {
    e.preventDefault()

    if (!iframe || !iframe.contentDocument) return null

    const iframeDocument = iframe.contentDocument

    // Hide the drop indicator and reset its styles
    if (dropIndicatorRef.current) {
        dropIndicatorRef.current.style.display = 'none'
        dropIndicatorRef.current.style.border = 'none'
    }

    if (!dragOverElemRef.current) return null

    // Get the target element
    const targetElement = dragOverElemRef.current

    // Check if this is a move operation or a new component
    const isMoveOperation = e.dataTransfer?.getData('moveElement') === 'true'

    let elementToPlace: HTMLElement | null = null

    if (isMoveOperation) {
        // This is a move operation - get the element being moved
        const elementId = e.dataTransfer?.getData('elementId')
        if (!elementId) return null

        const elementToMove = iframeDocument.getElementById(elementId)
        if (!elementToMove) return null

        // Don't allow dropping onto itself or its descendants
        if (elementToMove === targetElement || elementToMove.contains(targetElement)) {
            return null
        }

        // Remove the element from its current parent
        elementToMove.parentNode?.removeChild(elementToMove)

        // Set the element to place
        elementToPlace = elementToMove

        // Reset draggable attribute
        elementToPlace.setAttribute('draggable', 'false')
    } else {
        // This is a new component - create it
        const componentType = e.dataTransfer?.getData('component')
        if (!componentType) return null

        // Create the new element based on the component type
        elementToPlace = createComponentElement(componentType, iframeDocument)
        if (!elementToPlace) return null
    }

    // Handle placement based on the drop position
    if (dragPosition.current === 'inside' && isContainer(targetElement)) {
        // Insert as a child of the target element
        targetElement.appendChild(elementToPlace)
    } else {
        // Insert before or after the target element
        const parentNode = targetElement.parentNode
        if (!parentNode) return null

        if (dragPosition.current === 'above') {
            parentNode.insertBefore(elementToPlace, targetElement)
        } else { // 'below'
            parentNode.insertBefore(elementToPlace, targetElement.nextSibling)
        }
    }

    // Update the HTML content state
    onHtmlContentChange(iframeDocument.documentElement.outerHTML)

    // Reset refs
    dragOverElemRef.current = null

    return elementToPlace
}

/**
 * Handles dragleave events
 * @param e - The drag event
 * @param iframe - The iframe reference
 * @param dropIndicatorRef - Reference to the drop indicator element outside the iframe
 */
export const handleDragLeave = (
    e: DragEvent,
    iframe: HTMLIFrameElement,
    dropIndicatorRef: {current: HTMLDivElement | null}
): void => {
    if (!iframe || !dropIndicatorRef.current) return

    // Hide the indicator only if leaving the iframe
    if (e.relatedTarget === null || !(e.relatedTarget as HTMLElement).closest('iframe')) {
        dropIndicatorRef.current.style.display = 'none'
        // Reset any custom styles that might have been applied
        dropIndicatorRef.current.style.border = 'none'
        dropIndicatorRef.current.style.height = '2px'
    }
}

/**
 * Creates a component element based on the component type
 * @param componentType - The type of component to create
 * @param document - The document to create the element in
 * @returns The created element or null if the component type is not supported
 */
export const createComponentElement = (
    componentType: string,
    document: Document
): HTMLElement | null => {
    let newElement: HTMLElement

    switch (componentType) {
        case 'button':
            newElement = document.createElement('button')
            newElement.textContent = 'New Button'
            newElement.style.padding = '8px 16px'
            newElement.style.backgroundColor = '#3b82f6'
            newElement.style.color = 'white'
            newElement.style.border = 'none'
            newElement.style.borderRadius = '4px'
            newElement.style.cursor = 'pointer'
            break

        case 'text':
            newElement = document.createElement('div')
            newElement.textContent = 'New Text Component'
            newElement.style.padding = '8px'
            break

        case 'input':
            newElement = document.createElement('input')
            newElement.setAttribute('type', 'text')
            newElement.setAttribute('placeholder', 'Enter text...')
            newElement.style.padding = '8px'
            newElement.style.border = '1px solid #ccc'
            newElement.style.borderRadius = '4px'
            newElement.style.width = '100%'
            break

        case 'box':
            newElement = document.createElement('div')
            newElement.style.padding = '16px'
            newElement.style.border = '1px solid #ccc'
            newElement.style.borderRadius = '4px'
            newElement.style.backgroundColor = '#f9fafb'
            newElement.style.minHeight = '80px'
            newElement.style.minWidth = '120px'
            break

        default:
            return null
    }

    return newElement
}

// We no longer need to add styles to the iframe document since we're using an external indicator
