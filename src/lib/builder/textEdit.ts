/**
 * Text editing functionality for the website builder
 * Allows users to edit text content of elements by double-clicking
 */

/**
 * Elements that should have special handling during text editing
 * to prevent their default behaviors
 */
const INTERACTIVE_ELEMENTS = ['BUTTON', 'A', 'INPUT', 'TEXTAREA', 'SELECT']

/**
 * Elements that can have their text content edited
 */
const TEXT_EDITABLE_ELEMENTS = [
    'DIV', 'SPAN', 'P', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6',
    'BUTTON', 'A', 'LI', 'TD', 'TH', 'LABEL', 'FIGCAPTION'
]

/**
 * Checks if an element's text content can be edited
 * @param element - The element to check
 * @returns True if the element can have its text content edited
 */
export const isTextEditable = (element: HTMLElement): boolean => {
    return TEXT_EDITABLE_ELEMENTS.includes(element.tagName) && element.innerText === element.innerHTML
}

/**
 * Checks if an element is interactive (needs special handling)
 * @param element - The element to check
 * @returns True if the element is interactive
 */
export const isInteractiveElement = (element: HTMLElement): boolean => {
    return INTERACTIVE_ELEMENTS.includes(element.tagName)
}

/**
 * Creates a double-click handler for text editing
 * @param element - The HTML element to handle double-clicks for
 * @param onHtmlContentChange - Callback to update HTML content
 * @param onElementSelect - Callback to update selected element
 * @param iframe - Reference to the iframe
 * @param onContentUpdate - Optional callback to save content to file
 * @returns A function that handles double-click events
 */
export const createTextEditHandler = (
    element: HTMLElement,
    onHtmlContentChange: (content: string) => void,
    onElementSelect: (element?: HTMLElement ) => void,
    iframe: HTMLIFrameElement,
    onContentUpdate?: () => void
) => (e: MouseEvent) => {
    // Only proceed if the element is text-editable
    if (!isTextEditable(element)) return

    e.stopPropagation()
    e.preventDefault()

    // Store the original content for potential cancellation
    const originalContent = element.textContent || ''

    // Create an editable div to replace the element's content
    const editableDiv = document.createElement('div')
    editableDiv.contentEditable = 'true'
    editableDiv.textContent = originalContent
    editableDiv.style.width = '100%'
    editableDiv.style.height = 'min-content'
    editableDiv.style.outline = 'none'
    // editableDiv.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
    // editableDiv.style.padding = getComputedStyle(element).padding;

    // Clear the element's content and append the editable div
    element.innerHTML = ''
    element.appendChild(editableDiv)

    // Focus the editable div and position cursor at click location
    editableDiv.focus()

    // Create a range and position it at the click point
    // We need to use the document from the iframe
    const iframeDocument = iframe.contentDocument as Document

    // Get the selection object from the iframe document
    const iframeWindow = iframe.contentWindow
    const selection = iframeWindow?.getSelection()
    if (!selection) return

    // Get the click coordinates relative to the iframe
    const relativeX = e.clientX
    const relativeY = e.clientY

    // Clear any existing selection
    selection.removeAllRanges()

    const position = iframeDocument.caretPositionFromPoint(relativeX, relativeY)
    if (position) {
        const range = iframeDocument.createRange()
        range.setStart(position.offsetNode, position.offset)
        range.collapse(true)
        selection.addRange(range)
    }

    // Handle keydown events in the editable div
    const handleKeyDown = (e: KeyboardEvent) => {
        // For interactive elements like buttons, prevent default space behavior
        if (isInteractiveElement(element) && e.key === ' ') {
            e.stopPropagation()
        }

        if (e.key == 'Enter' && !e.shiftKey) {
            e.preventDefault()
            element.ownerDocument?.defaultView?.document.execCommand('insertLineBreak')
        }

        // Handle Escape key to cancel editing
        if (e.key === 'Escape') {
            element.textContent = originalContent
            finishEditing()
        }

        onContentUpdate?.()
    }

    // Handle keyup events to update the overlay in real-time
    const handleKeyUp = () => {
        // Update the HTML content state on each keypress to update the overlay
        if (iframe.contentDocument) {
            // const updatedHtmlContent = iframe.contentDocument.documentElement.outerHTML;
            // onHtmlContentChange(updatedHtmlContent);

            // Save content to file if onContentUpdate is provided
            onContentUpdate?.()
        }
    }

    // Handle click outside to finish editing
    const handleClickOutside = (e: MouseEvent) => {
        if (!editableDiv.contains(e.target as Node)) {
            finishEditing()
        }
    }

    // Add event listeners
    editableDiv.addEventListener('keydown', handleKeyDown)
    editableDiv.addEventListener('keyup', handleKeyUp)
    iframeDocument.addEventListener('mousedown', handleClickOutside)

    // Function to finish editing and clean up
    const finishEditing = () => {
        // Remove event listeners
        // editableDiv.removeEventListener('keydown', handleKeyDown)
        // editableDiv.removeEventListener('keyup', handleKeyUp)
        iframeDocument.removeEventListener('mousedown', handleClickOutside)

        // Get the edited content
        const newContent = editableDiv.textContent || ''

        // Update the element's content
        element.textContent = newContent

        // Update the HTML content state
        const updatedHtmlContent = iframeDocument.documentElement.outerHTML
        onHtmlContentChange(updatedHtmlContent)

        onContentUpdate?.()

        // Re-select the element to update the properties panel
        onElementSelect(element)
    }
}
