import { Button } from '@/components/ui/button'
import { ArrowLeft, FileText, User, Building, ShoppingBag } from 'lucide-react'

interface TemplateSelectionScreenProps {
    onTemplateSelect: (templateType: 'blank' | 'personal' | 'business' | 'shop') => void
    onBack: () => void
}

export default function TemplateSelectionScreen({ onTemplateSelect, onBack }: TemplateSelectionScreenProps) {
    const templates = [
        {
            id: 'blank' as const,
            title: 'Blank Template',
            description: 'Start from scratch with a completely blank canvas',
            icon: FileText,
            color: 'bg-slate-500',
            preview: 'A minimal template with just a paragraph explaining it\'s blank'
        },
        {
            id: 'personal' as const,
            title: 'Personal Website',
            description: 'Perfect for portfolios, resumes, and personal branding',
            icon: User,
            color: 'bg-blue-500',
            preview: 'Hero section, about me, skills showcase, and contact information'
        },
        {
            id: 'business' as const,
            title: 'Business Landing Page',
            description: 'Professional landing page for businesses and services',
            icon: Building,
            color: 'bg-purple-500',
            preview: 'Hero with CTA, features section, and professional design'
        },
        {
            id: 'shop' as const,
            title: 'Online Shop',
            description: 'E-commerce template with product showcase and cart',
            icon: ShoppingBag,
            color: 'bg-red-500',
            preview: 'Product grid, shopping cart, and e-commerce features'
        }
    ]

    return (
        <div className="flex items-center justify-center w-full h-full bg-background">
            <div className="max-w-4xl mx-auto text-center space-y-8 p-8">
                {/* Header Section */}
                <div className="space-y-4">
                    <Button 
                        onClick={onBack}
                        variant="ghost"
                        className="absolute top-8 left-8"
                    >
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Back
                    </Button>
                    
                    <h1 className="text-4xl font-bold text-foreground">Choose a Template</h1>
                    <p className="text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                        Select a template that best fits your needs. You can customize everything later using our visual editor.
                    </p>
                </div>

                {/* Templates Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-12">
                    {templates.map((template) => {
                        const IconComponent = template.icon
                        return (
                            <div
                                key={template.id}
                                className="group relative bg-card border rounded-xl p-6 hover:shadow-lg transition-all duration-300 cursor-pointer"
                                onClick={() => onTemplateSelect(template.id)}
                            >
                                {/* Template Icon */}
                                <div className={`w-16 h-16 ${template.color} rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                                    <IconComponent className="w-8 h-8 text-white" />
                                </div>

                                {/* Template Info */}
                                <div className="text-left space-y-3">
                                    <h3 className="text-xl font-semibold text-card-foreground group-hover:text-primary transition-colors">
                                        {template.title}
                                    </h3>
                                    <p className="text-muted-foreground">
                                        {template.description}
                                    </p>
                                    <p className="text-sm text-muted-foreground/80 italic">
                                        {template.preview}
                                    </p>
                                </div>

                                {/* Hover Effect */}
                                <div className="absolute inset-0 bg-primary/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                                
                                {/* Select Button */}
                                <div className="mt-4">
                                    <Button 
                                        className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors"
                                        variant="outline"
                                    >
                                        Select Template
                                    </Button>
                                </div>
                            </div>
                        )
                    })}
                </div>

                {/* Additional Info */}
                <div className="mt-8 p-4 bg-muted/50 rounded-lg">
                    <p className="text-sm text-muted-foreground">
                        💡 <strong>Tip:</strong> Don't worry about choosing the perfect template. 
                        You can always modify the design, content, and layout using our visual editor after selection.
                    </p>
                </div>
            </div>
        </div>
    )
}
