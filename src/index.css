@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Glassmorphism Dark Theme */
    --background: 240 10% 15%; /* Very dark background like #0a0a0a */
    --foreground: 210 20% 90%; /* Light text */

    --muted: 240 10% 8%;
    --muted-foreground: 215 20% 65%;

    --popover: 240 10% 8%;
    --popover-foreground: 210 20% 90%;

    --border: 215 30% 33%; /* White with 10% opacity */
    --input: 0 0% 100% / 0.1;

    --card: 0 0% 100% / 0.05; /* Glass card background */
    --card-foreground: 210 20% 90%;

    /*--primary: 238 100% 77%; !* Indigo-500 #6366f1 *!*/
    /*--primary: 265 44.77% 50.2%; !* Indigo-500 #6366f1 *!*/
    --primary: 248.21 76.06% 75.9%; /* Indigo-500 #6366f1 */
    --primary-foreground: 240 10% 4%;

    --secondary: 0 0% 100% / 0.08; /* Glass secondary */
    --secondary-foreground: 210 20% 90%;

    --accent: 0 0% 100% / 0.08; /* Glass accent */
    --accent-foreground: 210 20% 90%;

    --destructive: 0 63% 50%;
    --destructive-foreground: 210 20% 90%;

    --ring: 238 83% 67%; /* Indigo ring */

    --radius: 0.6rem; /* Larger radius for glass effect */

    --font-sans: 'Inter', sans-serif;

    /* Glassmorphism specific variables */
    --glass-bg: 0 0% 100% / 0.05;
    --glass-bg-hover: 0 0% 100% / 0.08;
    --glass-border: 0 0% 100% / 0.1;
    --glass-border-hover: 238 83% 67% / 0.3;

    /* Gradient colors */
    --gradient-primary: 238 83% 67%; /* #6366f1 */
    --gradient-secondary: 262 78% 70%; /* #8b5cf6 */
    --gradient-accent: 270 91% 65%; /* #a855f7 */
  }

  .light {
    /* Light theme (keeping as fallback) */
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;

    --ring: 215 20.2% 65.1%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    font-size: 14px;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;

    /* Animated gradient background similar to landing page */
    background:
      /*radial-gradient(circle at 20% 50%, hsla(var(--gradient-primary) / 0.3) 0%, transparent 50%),*/
      /*radial-gradient(circle at 80% 20%, hsla(var(--gradient-secondary) / 0.3) 0%, transparent 50%),*/
      /*radial-gradient(circle at 40% 80%, hsla(var(--gradient-accent) / 0.2) 0%, transparent 50%),*/
      hsl(var(--background));
  }
}

/*@layer components {*/
/*  !* Glass morphism utilities *!*/
/*  .glass {*/
/*    @apply bg-glass backdrop-blur-xl border border-glass-border;*/
/*  }*/

/*  .glass-hover {*/
/*    @apply hover:bg-glass-hover hover:border-glass-border-hover;*/
/*  }*/

/*  .glass-card {*/
/*    @apply glass glass-hover rounded-2xl transition-all duration-300;*/
/*  }*/

/*  .glass-card:hover {*/
/*    @apply -translate-y-1 shadow-2xl shadow-black/40;*/
/*  }*/

/*  !* Gradient text utility *!*/
/*  .gradient-text {*/
/*    @apply bg-gradient-to-r from-gradient-primary via-gradient-secondary to-gradient-accent bg-clip-text text-transparent;*/
/*  }*/

/*  !* Glass button utilities *!*/
/*  .btn-glass {*/
/*    @apply glass glass-hover px-6 py-3 rounded-2xl font-semibold transition-all duration-300;*/
/*  }*/

/*  .btn-glass:hover {*/
/*    @apply -translate-y-0.5 shadow-lg shadow-primary/30;*/
/*  }*/

/*  .btn-glass-primary {*/
/*    @apply bg-primary/20 border-primary/30 text-white hover:bg-primary/30;*/
/*  }*/
/*}*/
